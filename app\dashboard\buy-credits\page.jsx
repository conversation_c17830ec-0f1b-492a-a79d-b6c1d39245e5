"use client"
import { UserDetailContext } from '@/app/_context/UserDetailContext';
import { Button } from '@/components/ui/button';
import { db } from '@/config/db';
import { Users } from '@/config/schema';
import { PayPalButtons } from '@paypal/react-paypal-js';
import { useRouter } from 'next/navigation';
import { eq } from 'drizzle-orm';

import React, { useContext, useState, useRef } from 'react'
import { Check } from 'lucide-react'

function BuyCredits() {
    const creditsOption=[
        {
          credits:5,
          amount:0.99
        },
        {
          credits:10,
          amount:1.99
        },
        {
          credits:25,
          amount:3.99
        },
        {
          credits:50,
          amount:6.99
        },
        // {
        //   credits:100,
        //   amount:9.99
        // },
      ]
    
      const [selectedOption,setSelectedOption]=useState([]);
      const {userDetail,setUserDetail}=useContext(UserDetailContext);
      const router=useRouter();
      const paypalSectionRef = useRef(null);
      const onPaymentSuccess=async()=>{
        console.log("payment Success...")

        if (!userDetail?.email) {
          console.error('No user email found for credit update');
          return;
        }

        console.log('Adding credits for user:', userDetail.email);
        console.log('Current credits:', userDetail.credits);
        console.log('Credits to add:', selectedOption?.credits);

        try {
          //Update User Credits in DB
          const result=await db.update(Users)
          .set({
              credits:userDetail?.credits+selectedOption?.credits
          }).where(eq(Users.email, userDetail.email))
          .returning({id:Users.id, credits:Users.credits});

          console.log('Credit purchase result:', result);

          if(result && result.length > 0)
          {
                  setUserDetail(prev=>({
                      ...prev,
                      credits:userDetail?.credits+selectedOption?.credits
                  }))
                  router.push('/dashboard');
          }
        } catch (error) {
          console.error('Error updating user credits:', error);
        }
      }

      const features = [
        "Get 3 Room Designs Free – No Credit Card Needed",
        "AI-Powered Instant Designs",
        "Virtual Staging for Any Space",
        "Works with Your Real Photos",
        "Access to 50+ Interior Design Styles",
        "Beginner-Friendly – No Experience Needed",
        "Before & After Comparisons",
        "Pay Only for What You Use – No Subscription",
        "Personalize Your Design – Add Custom Reques",
        "Credits Never Expire",
        
      ];

  return (
    <div>
        <h2 className='font-bold text-2xl'>Get More Credits</h2>
        <p>Buy credits and explore unlimited room transformations with AI. ✨🛋️</p>

        {/* Features Section */}
        <div className='mt-8 mb-10'>
          <div className='bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 rounded-xl p-6 border border-blue-100 dark:border-blue-800'>
            <h3 className='font-bold text-xl mb-4 text-center text-gray-800 dark:text-gray-200'>
              What You Get with RoomDesignsAI
            </h3>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-3'>
              {features.map((feature, index) => (
                <div key={index} className='flex items-start gap-3'>
                  <Check className='h-5 w-5 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0' />
                  <span className='text-base text-gray-700 dark:text-gray-300 leading-relaxed'>
                    {feature}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-5 mt-10'>
            {creditsOption.map((item,index)=>(
              <div key={index} className={`flex flex-col gap-2 justify-center items-center border shadow-md rounded-lg p-5
                ${selectedOption?.credits==item.credits&&'border-primary'}
                `}
              >
                <h2 className='font-bold text-3xl'>{item.credits}</h2>
                <h2 className='font-medium text-xl'>Credits</h2>

                <Button className="w-full" onClick={()=>{
                  setSelectedOption(item);
                  // Smooth scroll to PayPal section after a short delay
                  setTimeout(() => {
                    paypalSectionRef.current?.scrollIntoView({
                      behavior: 'smooth',
                      block: 'start'
                    });
                  }, 100);
                }}>Select</Button>
                <h2 className='font-medium text-primary'>${item.amount}</h2>
              </div>
            ))}
        </div>

        <div className='mt-20' ref={paypalSectionRef}>
            {selectedOption?.amount&&
                <PayPalButtons style={{ layout: "horizontal" }}
                    onApprove={()=>onPaymentSuccess()}
                    onCancel={()=>console.log("Payment Cancel")}
                    createOrder={(data,actions)=>{
                        return actions?.order.create({
                            purchase_units:[
                                {
                                    amount:{
                                        value:selectedOption?.amount?.toFixed(2),
                                        currency_code:'USD'
                                    }
                                }
                            ]
                        })
                    }}
                />
            }
        </div>


    </div>
  )
}

export default BuyCredits