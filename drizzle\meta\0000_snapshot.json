{"id": "37923f51-1be8-47a1-9d1c-8b8089979deb", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.aiGeneratedImage": {"name": "aiGeneratedImage", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "roomType": {"name": "roomType", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "designType": {"name": "designType", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "orgImage": {"name": "orgImage", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "aiImage": {"name": "aiImage", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "userEmail": {"name": "userEmail", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "imageUrl": {"name": "imageUrl", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "credits": {"name": "credits", "type": "integer", "primaryKey": false, "notNull": false, "default": 3}, "isAdmin": {"name": "isAdmin", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}