"use client"
import Image from 'next/image'
import React, { useState, useImperativeHandle, forwardRef } from 'react'
import { cn } from "@/lib/utils"
import { AlertCircle } from "lucide-react"

const ImageSelection = forwardRef(function ImageSelection({ value, selectedImage, onValidationChange }, ref) {

    const [error, setError] = useState("")
    const [touched, setTouched] = useState(false)

    // Expose validation method to parent component
    useImperativeHandle(ref, () => ({
        validate: () => {
            const isValid = validateSelection(value)
            setTouched(true)
            return isValid
        },
        reset: () => {
            // Parent will handle resetting the value
            setError("")
            setTouched(false)
        },
        getValue: () => value
    }))

    const validateSelection = (currentFile) => {
        if (!currentFile) {
            setError("Please select an image of your room")
            onValidationChange?.(false)
            return false
        }
        setError("")
        onValidationChange?.(true)
        return true
    }

    // Function to convert PNG to JPEG for better AI compatibility
    const convertPngToJpeg = (file) => {
        return new Promise((resolve) => {
            if (file.type !== 'image/png') {
                resolve(file);
                return;
            }

            console.log('Converting PNG to JPEG for better AI compatibility...');

            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            // Use HTMLImageElement constructor explicitly to avoid conflict with Next.js Image
            const img = document.createElement('img');

            img.onload = () => {
                canvas.width = img.width;
                canvas.height = img.height;

                // Fill with white background to replace transparency
                ctx.fillStyle = 'white';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // Draw the image on top
                ctx.drawImage(img, 0, 0);

                // Convert to JPEG blob
                canvas.toBlob((blob) => {
                    // Create a new File object with JPEG format
                    const jpegFile = new File([blob], file.name.replace('.png', '.jpg'), {
                        type: 'image/jpeg',
                        lastModified: Date.now()
                    });

                    console.log('PNG converted to JPEG:', {
                        originalSize: file.size,
                        newSize: jpegFile.size,
                        originalType: file.type,
                        newType: jpegFile.type
                    });

                    resolve(jpegFile);
                }, 'image/jpeg', 0.9);
            };

            img.onerror = () => {
                console.error('Failed to load image for conversion');
                resolve(file); // Return original file if conversion fails
            };

            img.src = URL.createObjectURL(file);
        });
    };

    // Function to convert HEIC to JPEG for iPhone compatibility
    const convertHeicToJpeg = async (file) => {
        if (file.type !== 'image/heic' && file.type !== 'image/heif' && !file.name.toLowerCase().endsWith('.heic') && !file.name.toLowerCase().endsWith('.heif')) {
            return file;
        }

        console.log('Converting HEIC to JPEG for iPhone compatibility...');

        try {
            // Dynamic import to avoid SSR issues
            const heic2any = (await import('heic2any')).default;

            const convertedBlob = await heic2any({
                blob: file,
                toType: 'image/jpeg',
                quality: 0.9
            });

            // heic2any might return an array of blobs, so handle both cases
            const blob = Array.isArray(convertedBlob) ? convertedBlob[0] : convertedBlob;

            // Create a new File object with JPEG format
            const jpegFile = new File([blob], file.name.replace(/\.(heic|heif)$/i, '.jpg'), {
                type: 'image/jpeg',
                lastModified: Date.now()
            });

            console.log('HEIC converted to JPEG:', {
                originalSize: file.size,
                newSize: jpegFile.size,
                originalType: file.type,
                newType: jpegFile.type
            });

            return jpegFile;
        } catch (error) {
            console.error('Failed to convert HEIC to JPEG:', error);
            // Return original file if conversion fails
            return file;
        }
    };

    const onFileSelected = async (event) => {
        const selectedFile = event.target.files[0]
        console.log('Selected file:', {
            name: selectedFile?.name,
            size: selectedFile?.size,
            type: selectedFile?.type,
            lastModified: selectedFile?.lastModified
        });

        // Convert HEIC to JPEG first (for iPhone images)
        let processedFile = await convertHeicToJpeg(selectedFile);

        // Then convert PNG to JPEG if needed
        processedFile = await convertPngToJpeg(processedFile);

        setTouched(true)
        validateSelection(processedFile)
        selectedImage(processedFile)
    }

    const hasError = error && touched

  return (
    <div>
        <label className={cn(
            "text-sm font-medium",
            hasError ? "text-red-600" : "text-muted-foreground"
        )}>
            Select Image of your room *
        </label>
        <div className='mt-3'>
            <label htmlFor='upload-image'>
                <div className={cn(
                    "border rounded-xl border-dotted flex justify-center cursor-pointer hover:shadow-lg transition-colors",
                    value ? 'p-0 bg-white' : 'p-28',
                    hasError ? [
                        "border-red-500 bg-red-50 dark:bg-red-950/10"
                    ] : [
                        "border-primary bg-slate-200"
                    ]
                )}>
                   {!value? <Image src={'/imageupload.png'} width={70} height={70} alt="Upload Image"/>
                                      :<Image src={URL.createObjectURL(value)} width={300} height={300}
                   className='w-[300px] h-[300px] object-cover' alt="Selected Room Image"
                   />}
                </div>
            </label>
            <input type="file" accept='image/png, image/jpeg, image/heic, image/heif, .heic, .heif'
            id="upload-image"
            style={{display:'none'}}
            onChange={onFileSelected}
            />
        </div>

        {/* Error Message */}
        {hasError && (
            <div
                className="flex items-center gap-2 text-sm text-red-600 mt-2"
                role="alert"
                aria-live="polite"
            >
                <AlertCircle className="h-4 w-4 flex-shrink-0" />
                <span>{error}</span>
            </div>
        )}

        {/* Success Indicator */}
        {value && !hasError && touched && (
            <div className="flex items-center gap-2 text-sm text-primary mt-2">
                <div className="h-4 w-4 rounded-full bg-primary/10 flex items-center justify-center">
                    <div className="h-2 w-2 rounded-full bg-primary"></div>
                </div>
                <span>Image selected</span>
            </div>
        )}

        {/* Handwritten Tip */}
        <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 rounded-xl border-2 border-dashed border-blue-200 dark:border-blue-800">
          <div className="flex items-start gap-3">
            <div className="text-2xl">💡</div>
            <div className="flex-1">
              <p
                className="handwriting text-lg font-medium text-blue-800 dark:text-blue-200 leading-relaxed"
                style={{
                  transform: "rotate(-0.5deg)",
                  textShadow: "1px 1px 2px rgba(0,0,0,0.1)"
                }}
              >
                <span className="text-xl">✨ Pro Tip:</span> Start simple! Try generating your first design with just{" "}
                <span className="font-bold underline decoration-wavy decoration-yellow-400">Room Type</span> and{" "}
                <span className="font-bold underline decoration-wavy decoration-accent">Design Style</span> selected.{" "}
                <br />
                <span className="text-base italic mt-1 inline-block" style={{ transform: "rotate(0.3deg)" }}>
                  You can always add mood & features later for more customization! 🎨
                </span>
                <br />
                <span className="text-sm text-green-700 dark:text-green-300 mt-2 inline-block" style={{ transform: "rotate(0.2deg)" }}>
                  📱 iPhone users: HEIC photos are automatically converted to JPEG!
                </span>
              </p>
            </div>
          </div>
        </div>
    </div>
  )
})

export default ImageSelection