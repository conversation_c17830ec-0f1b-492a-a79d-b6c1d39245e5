"use client"
import React, { useState } from 'react'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import axios from 'axios'

export default function DebugPage() {
    const [file, setFile] = useState(null)
    const [logs, setLogs] = useState([])
    const [loading, setLoading] = useState(false)

    const addLog = (message, type = 'info') => {
        const timestamp = new Date().toLocaleTimeString()
        setLogs(prev => [...prev, { message, type, timestamp }])
        console.log(`[${type.toUpperCase()}] ${message}`)
    }

    const analyzeFile = (file) => {
        if (!file) return

        addLog(`File selected: ${file.name}`)
        addLog(`File size: ${file.size} bytes`)
        addLog(`File type: ${file.type}`)
        addLog(`Last modified: ${new Date(file.lastModified).toLocaleString()}`)

        // Read first few bytes to check file signature
        const reader = new FileReader()
        reader.onload = (e) => {
            const arrayBuffer = e.target.result
            const bytes = new Uint8Array(arrayBuffer.slice(0, 10))
            const signature = Array.from(bytes).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' ')
            addLog(`File signature: ${signature}`)

            // Check if it's a valid PNG
            if (bytes[0] === 0x89 && bytes[1] === 0x50 && bytes[2] === 0x4E && bytes[3] === 0x47) {
                addLog('✅ Valid PNG signature detected', 'success')
            } else if (bytes[0] === 0xFF && bytes[1] === 0xD8) {
                addLog('✅ Valid JPEG signature detected', 'success')
            } else {
                addLog('❌ Unknown file signature', 'error')
            }
        }
        reader.readAsArrayBuffer(file)
    }

    const convertPngToJpeg = (file) => {
        return new Promise((resolve) => {
            if (file.type !== 'image/png') {
                addLog('File is not PNG, no conversion needed')
                resolve(file);
                return;
            }

            addLog('🔄 Converting PNG to JPEG...')

            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = document.createElement('img');

            img.onload = () => {
                canvas.width = img.width;
                canvas.height = img.height;

                // Fill with white background to replace transparency
                ctx.fillStyle = 'white';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // Draw the image on top
                ctx.drawImage(img, 0, 0);

                // Convert to JPEG blob
                canvas.toBlob((blob) => {
                    // Create a new File object with JPEG format
                    const jpegFile = new File([blob], file.name.replace('.png', '.jpg'), {
                        type: 'image/jpeg',
                        lastModified: Date.now()
                    });

                    addLog(`✅ PNG converted to JPEG (${file.size} → ${jpegFile.size} bytes)`, 'success');
                    resolve(jpegFile);
                }, 'image/jpeg', 0.9);
            };

            img.onerror = () => {
                addLog('❌ Failed to load image for conversion', 'error');
                resolve(file); // Return original file if conversion fails
            };

            img.src = URL.createObjectURL(file);
        });
    };

    const testFirebaseUpload = async () => {
        if (!file) {
            addLog('❌ No file selected', 'error')
            return
        }

        setLoading(true)
        addLog('🔄 Testing Firebase upload...')

        try {
            // Import Firebase functions dynamically
            const { ref, uploadBytes, getDownloadURL } = await import('firebase/storage')
            const { storage } = await import('@/config/firebaseConfig')

            const fileName = `debug_${Date.now()}_${file.name}`
            const imageRef = ref(storage, 'room-redesign/' + fileName)

            addLog(`Uploading to Firebase: ${fileName}`)
            
            const uploadResult = await uploadBytes(imageRef, file)
            addLog('✅ Firebase upload successful', 'success')
            
            const downloadUrl = await getDownloadURL(imageRef)
            addLog(`✅ Download URL obtained: ${downloadUrl}`, 'success')
            
            return downloadUrl
        } catch (error) {
            addLog(`❌ Firebase upload failed: ${error.message}`, 'error')
            throw error
        } finally {
            setLoading(false)
        }
    }

    const testApiCall = async (imageUrl) => {
        if (!imageUrl) {
            addLog('❌ No image URL provided', 'error')
            return
        }

        setLoading(true)
        addLog('🔄 Testing API call...')

        try {
            const result = await axios.post('/api/redesign-room', {
                imageUrl: imageUrl,
                roomType: 'Living Room',
                designType: 'Modern',
                mood: ['Cozy'],
                roomFeatures: ['Natural Light'],
                additionalReq: 'Debug test',
                userEmail: '<EMAIL>'
            })

            addLog('✅ API call successful', 'success')
            addLog(`Generated image: ${result.data.result}`, 'success')
            
            return result.data.result
        } catch (error) {
            addLog(`❌ API call failed: ${error.response?.data?.error || error.message}`, 'error')
            if (error.response?.data?.details) {
                addLog(`Error details: ${error.response.data.details}`, 'error')
            }
            throw error
        } finally {
            setLoading(false)
        }
    }

    const runFullTest = async () => {
        if (!file) {
            addLog('❌ Please select a file first', 'error')
            return
        }

        setLogs([])
        addLog('🚀 Starting full debug test...')

        try {
            // Step 1: Analyze file
            analyzeFile(file)

            // Step 2: Convert PNG to JPEG if needed
            const processedFile = await convertPngToJpeg(file)

            // Step 3: Test Firebase upload with processed file
            const imageUrl = await testFirebaseUploadWithFile(processedFile)

            // Step 4: Test API call
            await testApiCall(imageUrl)

            addLog('🎉 All tests completed successfully!', 'success')
        } catch (error) {
            addLog(`💥 Test failed: ${error.message}`, 'error')
        }
    }

    const testFirebaseUploadWithFile = async (fileToUpload) => {
        if (!fileToUpload) {
            addLog('❌ No file provided', 'error')
            return
        }

        setLoading(true)
        addLog('🔄 Testing Firebase upload...')

        try {
            // Import Firebase functions dynamically
            const { ref, uploadBytes, getDownloadURL } = await import('firebase/storage')
            const { storage } = await import('@/config/firebaseConfig')

            const fileName = `debug_${Date.now()}_${fileToUpload.name}`
            const imageRef = ref(storage, 'room-redesign/' + fileName)

            addLog(`Uploading to Firebase: ${fileName}`)

            const uploadResult = await uploadBytes(imageRef, fileToUpload)
            addLog('✅ Firebase upload successful', 'success')

            const downloadUrl = await getDownloadURL(imageRef)
            addLog(`✅ Download URL obtained: ${downloadUrl}`, 'success')

            return downloadUrl
        } catch (error) {
            addLog(`❌ Firebase upload failed: ${error.message}`, 'error')
            throw error
        } finally {
            setLoading(false)
        }
    }

    const clearLogs = () => {
        setLogs([])
    }

    return (
        <div className="container mx-auto p-6 max-w-4xl">
            <h1 className="text-3xl font-bold mb-6">Debug Image Upload</h1>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* File Selection */}
                <div className="space-y-4">
                    <h2 className="text-xl font-semibold">File Selection</h2>
                    
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
                        <input
                            type="file"
                            accept="image/png,image/jpeg,image/jpg"
                            onChange={(e) => {
                                const selectedFile = e.target.files[0]
                                setFile(selectedFile)
                                if (selectedFile) {
                                    analyzeFile(selectedFile)
                                }
                            }}
                            className="w-full"
                        />
                        
                        {file && (
                            <div className="mt-4">
                                <Image
                                    src={URL.createObjectURL(file)}
                                    alt="Preview"
                                    width={200}
                                    height={192}
                                    className="max-w-full h-48 object-cover rounded"
                                />
                            </div>
                        )}
                    </div>
                    
                    <div className="space-y-2">
                        <Button
                            onClick={async () => {
                                if (file) {
                                    const converted = await convertPngToJpeg(file);
                                    setFile(converted);
                                }
                            }}
                            disabled={!file || loading || file?.type !== 'image/png'}
                            className="w-full"
                            variant="outline"
                        >
                            Convert PNG to JPEG
                        </Button>

                        <Button
                            onClick={testFirebaseUpload}
                            disabled={!file || loading}
                            className="w-full"
                        >
                            Test Firebase Upload
                        </Button>

                        <Button
                            onClick={runFullTest}
                            disabled={!file || loading}
                            className="w-full"
                            variant="default"
                        >
                            {loading ? 'Running Tests...' : 'Run Full Test (with PNG→JPEG)'}
                        </Button>
                    </div>
                </div>
                
                {/* Logs */}
                <div className="space-y-4">
                    <div className="flex justify-between items-center">
                        <h2 className="text-xl font-semibold">Debug Logs</h2>
                        <Button onClick={clearLogs} variant="outline" size="sm">
                            Clear Logs
                        </Button>
                    </div>
                    
                    <div className="bg-gray-100 rounded-lg p-4 h-96 overflow-y-auto font-mono text-sm">
                        {logs.length === 0 ? (
                            <p className="text-gray-500">No logs yet...</p>
                        ) : (
                            logs.map((log, index) => (
                                <div
                                    key={index}
                                    className={`mb-1 ${
                                        log.type === 'error' ? 'text-red-600' :
                                        log.type === 'success' ? 'text-green-600' :
                                        'text-gray-800'
                                    }`}
                                >
                                    <span className="text-gray-500">[{log.timestamp}]</span> {log.message}
                                </div>
                            ))
                        )}
                    </div>
                </div>
            </div>
        </div>
    )
}
