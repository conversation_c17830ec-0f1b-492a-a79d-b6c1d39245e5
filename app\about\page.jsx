"use client"

import React from 'react'
import { <PERSON><PERSON><PERSON>, Users, Target, Award, ArrowRight } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import BrandLogo from '@/components/ui/brand-logo'

const AboutPage = () => {
  const values = [
    {
      icon: Sparkles,
      title: "Innovation",
      description: "We leverage cutting-edge AI technology to revolutionize interior design, making professional-quality room transformations accessible to everyone."
    },
    {
      icon: Users,
      title: "User-Centric",
      description: "Every feature we build is designed with our users in mind, ensuring an intuitive and delightful experience for designers and homeowners alike."
    },
    {
      icon: Target,
      title: "Quality",
      description: "We're committed to delivering high-quality AI-generated designs that meet professional standards and exceed user expectations."
    },
    {
      icon: Award,
      title: "Excellence",
      description: "We strive for excellence in everything we do, from our AI algorithms to our customer support, ensuring the best possible experience."
    }
  ]

  const stats = [
    { number: "50K+", label: "Rooms Redesigned" },
    { number: "10K+", label: "Happy Users" },
    { number: "25+", label: "Design Styles" },
    { number: "99%", label: "Satisfaction Rate" }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-background/80">
      {/* Header */}
      <header className="border-b border-border">
        <div className="max-w-7xl mx-auto px-6 py-4 flex items-center justify-between">
          <Link href="/">
            <BrandLogo size="default" />
          </Link>
          <Link href="/dashboard">
            <Button>Dashboard</Button>
          </Link>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-6 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-foreground mb-6">
            About RoomDesignsAI
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
            We&apos;re on a mission to democratize interior design through the power of artificial intelligence, 
            making beautiful room transformations accessible to everyone, everywhere.
          </p>
        </div>

        {/* Mission Section */}
        <div className="mb-16">
          <Card className="p-8 bg-gradient-to-r from-primary/5 to-accent/5 border-primary/20">
            <CardContent className="text-center">
              <h2 className="text-3xl font-bold text-foreground mb-4">Our Mission</h2>
              <p className="text-lg text-muted-foreground max-w-4xl mx-auto">
                To empower individuals and professionals with AI-driven design tools that transform any space 
                into a beautiful, functional environment. We believe that great design should be accessible to 
                everyone, regardless of budget or design experience.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Stats Section - COMMENTED OUT FOR FUTURE USE */}
        {/*
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          {stats.map((stat, index) => (
            <Card key={index} className="text-center p-6">
              <CardContent>
                <div className="text-3xl md:text-4xl font-bold text-primary mb-2">
                  {stat.number}
                </div>
                <div className="text-sm text-muted-foreground">
                  {stat.label}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        */}

        {/* Values Section */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-foreground mb-4">Our Values</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              These core values guide everything we do and shape the way we build our products and serve our community.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {values.map((value, index) => {
              const Icon = value.icon
              return (
                <Card key={index} className="p-6 hover:shadow-lg transition-shadow duration-300">
                  <CardContent>
                    <div className="flex items-start space-x-4">
                      <div className="flex-shrink-0">
                        <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                          <Icon className="h-6 w-6 text-primary" />
                        </div>
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold text-foreground mb-2">
                          {value.title}
                        </h3>
                        <p className="text-muted-foreground">
                          {value.description}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>

        {/* Story Section */}
        <div className="mb-16">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-foreground mb-6">Our Story</h2>
              <div className="space-y-4 text-muted-foreground">
                <p>
                  RoomDesignsAI was born from a simple observation: interior design should be accessible to everyone, 
                  not just those who can afford expensive designers or have years of design experience.
                </p>
                <p>
                  Our team of AI researchers, designers, and engineers came together with a shared vision to create 
                  a platform that could instantly transform any room using the power of artificial intelligence.
                </p>
                <p>
                  Today, we&apos;re proud to serve thousands of users worldwide, helping them visualize and create 
                  beautiful spaces that reflect their personal style and needs.
                </p>
              </div>
            </div>
            <div className="bg-gradient-to-br from-primary/10 to-accent/10 rounded-2xl p-8 text-center">
              <div className="text-6xl mb-4">🏠</div>
              <h3 className="text-2xl font-bold text-foreground mb-2">
                Transforming Spaces
              </h3>
              <p className="text-muted-foreground">
                One AI-powered design at a time
              </p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <Card className="p-8 bg-gradient-to-r from-primary/5 to-accent/5 border-primary/20">
            <CardContent>
              <h2 className="text-3xl font-bold text-foreground mb-4">
                Ready to Transform Your Space?
              </h2>
              <p className="text-lg text-muted-foreground mb-6 max-w-2xl mx-auto">
                Join thousands of users who have already discovered the power of AI-driven interior design.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/dashboard">
                  <Button size="lg" className="w-full sm:w-auto">
                    Get Started Free
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
                <Link href="/contact">
                  <Button variant="outline" size="lg" className="w-full sm:w-auto">
                    Contact Us
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default AboutPage
