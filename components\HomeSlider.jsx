"use client";

import { useRef, useCallback } from 'react';
import Image from 'next/image';

export default function HomeSlider() {
  const containerRef = useRef(null);
  const sliderRef = useRef(null);
  const clipRef = useRef(null);
  const positionRef = useRef(50);

  const updateSliderPosition = useCallback((clientX) => {
    if (!containerRef.current || !sliderRef.current || !clipRef.current) return;

    const container = containerRef.current;
    const rect = container.getBoundingClientRect();
    let x = clientX - rect.left;
    x = Math.max(0, Math.min(x, rect.width));
    const percent = (x / rect.width) * 100;

    positionRef.current = percent;

    // Direct DOM manipulation for better performance
    sliderRef.current.style.left = `${percent}%`;
    clipRef.current.style.clipPath = `polygon(0 0, ${percent}% 0, ${percent}% 100%, 0 100%)`;
  }, []);

  const handleStart = useCallback((clientX) => {
    setIsDragging(true);
    updateSliderPosition(clientX);

    const handleMove = (e) => {
      e.preventDefault();
      const x = e.type.includes('touch') ? e.touches[0].clientX : e.clientX;
      updateSliderPosition(x);
    };

    const handleEnd = (e) => {
      e.preventDefault();
      setIsDragging(false);
      document.removeEventListener('mousemove', handleMove);
      document.removeEventListener('mouseup', handleEnd);
      document.removeEventListener('touchmove', handleMove);
      document.removeEventListener('touchend', handleEnd);
    };

    document.addEventListener('mousemove', handleMove, { passive: false });
    document.addEventListener('mouseup', handleEnd, { passive: false });
    document.addEventListener('touchmove', handleMove, { passive: false });
    document.addEventListener('touchend', handleEnd, { passive: false });
  }, [updateSliderPosition]);

  const handleMouseDown = useCallback((e) => {
    e.preventDefault();
    handleStart(e.clientX);
  }, [handleStart]);

  const handleTouchStart = useCallback((e) => {
    e.preventDefault();
    handleStart(e.touches[0].clientX);
  }, [handleStart]);

  return (
    <div className="w-full flex justify-center p-4">
      <div
        ref={containerRef}
        className="relative w-full max-w-2xl aspect-video rounded-2xl overflow-hidden shadow-2xl bg-muted select-none"
        onMouseDown={handleMouseDown}
        onTouchStart={handleTouchStart}
        style={{ touchAction: 'none' }}
      >
        {/* Container for both images */}
        <div className="relative w-full h-full overflow-hidden">
          {/* AI Generated Design (Background - Right side) */}
          <div className="absolute inset-0 w-full h-full">
            <Image
              src="/sample-18.png"
              alt="AI Generated Room Design"
              fill
              className="object-cover pointer-events-none"
              priority
              draggable={false}
            />
          </div>

          {/* Original Image (Foreground - Left side, clipped) */}
          <div
            ref={clipRef}
            className="absolute inset-0 overflow-hidden"
            style={{
              clipPath: `polygon(0 0, 50% 0, 50% 100%, 0 100%)`,
              zIndex: 2
            }}
          >
            <Image
              src="/original_before.jpg"
              alt="Original Room Photo"
              fill
              className="object-cover pointer-events-none"
              priority
              draggable={false}
            />
          </div>

          {/* Slider Handle */}
          <div
            ref={sliderRef}
            className="absolute top-0 bottom-0 w-1 bg-primary cursor-ew-resize touch-none will-change-transform"
            style={{
              left: '50%',
              transform: 'translateX(-50%)',
              zIndex: 10
            }}
            onMouseDown={handleMouseDown}
            onTouchStart={handleTouchStart}
          >
            <div className="absolute -left-4 top-1/2 w-10 h-10 rounded-full bg-background border-2 border-primary flex items-center justify-center shadow-lg hover:shadow-xl transition-shadow duration-200 will-change-transform"
                 style={{ transform: 'translateY(-50%)' }}>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-primary pointer-events-none"
              >
                <path d="M8 3L4 7l4 4" />
                <path d="M16 3l4 4-4 4" />
              </svg>
            </div>
          </div>

          {/* Labels */}
          <div className="absolute top-4 left-4 z-20">
            <div className="bg-black/70 text-white px-3 py-1 rounded-full text-sm font-medium">
              Before
            </div>
          </div>
          <div className="absolute top-4 right-4 z-20">
            <div className="bg-black/70 text-white px-3 py-1 rounded-full text-sm font-medium">
              After
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}