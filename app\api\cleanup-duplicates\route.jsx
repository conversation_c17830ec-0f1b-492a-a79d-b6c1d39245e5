import { db } from "@/config/db";
import { Users } from "@/config/schema";
import { NextResponse } from "next/server";
import { eq } from "drizzle-orm";
import { auth } from "@clerk/nextjs/server";

async function checkAdminAccess(req) {
    try {
        // Get user email from request headers
        const userEmail = req.headers.get('x-user-email');
        const adminToken = req.headers.get('x-admin-token');

        console.log('Admin access check:', { userEmail, hasToken: !!adminToken });

        if (!userEmail) {
            console.log('No user email provided');
            return { isAdmin: false, error: 'User email required' };
        }

        // Check user's admin status in database
        const userInfo = await db.select().from(Users)
            .where(eq(Users.email, userEmail));

        if (userInfo.length === 0) {
            console.log(`User not found in database: ${userEmail}`);
            return { isAdmin: false, error: 'User not found' };
        }

        const user = userInfo[0];
        if (user.isAdmin) {
            console.log(`Admin access granted for: ${userEmail}`);
            return { isAdmin: true, userEmail, user };
        }

        // Fallback: Allow access in development mode with valid token
        if (process.env.NODE_ENV === 'development' && adminToken === 'dev-token') {
            console.log(`Development access granted for: ${userEmail}`);
            return { isAdmin: true, userEmail, user };
        }

        console.log(`Access denied for: ${userEmail} (isAdmin: ${user.isAdmin})`);
        return { isAdmin: false, error: 'Access denied - admin privileges required' };
    } catch (error) {
        console.error('Error checking admin access:', error);
        return { isAdmin: false, error: 'Access check failed' };
    }
}

export async function GET(req) {
    // Check admin access
    const { isAdmin, error, userEmail } = await checkAdminAccess(req);

    if (!isAdmin) {
        console.log(`Unauthorized access attempt to cleanup-duplicates by: ${userEmail || 'unknown'}`);
        return NextResponse.json({
            success: false,
            error: 'Access denied. Admin privileges required.'
        }, { status: 403 });
    }

    console.log(`Admin access granted to cleanup-duplicates for: ${userEmail}`);
    try {
        console.log('Starting duplicate cleanup process...');
        
        // Get all users
        const allUsers = await db.select().from(Users);
        console.log(`Found ${allUsers.length} total users`);
        
        // Group users by email
        const emailMap = new Map();
        const duplicatesToDelete = [];
        
        allUsers.forEach(user => {
            if (emailMap.has(user.email)) {
                // This is a duplicate, mark for deletion
                duplicatesToDelete.push(user);
                console.log(`Found duplicate user: ${user.email} (ID: ${user.id})`);
            } else {
                // First occurrence, keep it
                emailMap.set(user.email, user);
                console.log(`Keeping user: ${user.email} (ID: ${user.id})`);
            }
        });
        
        if (duplicatesToDelete.length > 0) {
            console.log(`Deleting ${duplicatesToDelete.length} duplicate users...`);
            
            // Delete duplicate users one by one
            for (const user of duplicatesToDelete) {
                await db.delete(Users).where(eq(Users.id, user.id));
                console.log(`Deleted duplicate user: ${user.email} (ID: ${user.id})`);
            }
            
            // Get updated user list
            const updatedUsers = await db.select().from(Users);
            
            return NextResponse.json({
                success: true,
                message: `Successfully removed ${duplicatesToDelete.length} duplicate users`,
                deletedUsers: duplicatesToDelete.map(u => ({ id: u.id, email: u.email })),
                remainingUsers: updatedUsers.length,
                users: updatedUsers
            });
        } else {
            return NextResponse.json({
                success: true,
                message: 'No duplicate users found',
                users: allUsers
            });
        }
        
    } catch (error) {
        console.error('Error in cleanup-duplicates:', error);
        return NextResponse.json({
            success: false,
            error: error.message || 'Internal server error'
        }, { status: 500 });
    }
}

export async function POST(req) {
    // Same functionality as GET for convenience, but with admin check
    return GET(req);
}
