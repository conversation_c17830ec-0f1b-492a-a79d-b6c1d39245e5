import React from 'react';
import ReactBeforeSliderComponent from 'react-before-after-slider-component';
import 'react-before-after-slider-component/dist/build.css';
import { Button } from '@/components/ui/button';
import { Trash2 } from 'lucide-react';

function RoomDesignCard({ room, onDelete }) {
  const handleDelete = (e) => {
    e.stopPropagation();
    onDelete(room.id);
  };

  return (
    <div className='shadow-md rounded-md cursor-pointer relative'>
      <ReactBeforeSliderComponent
        firstImage={{
          imageUrl: room?.aiImage,
        }}
        secondImage={{
          imageUrl: room?.orgImage,
        }}
        delimiterColor='#3b82f6'
      />
      <div className='p-4'>
        <h2>🏡 Room Type: {room.roomType}</h2>
        <h2>🎨 Design Type: {room.designType}</h2>
      </div>
      <div className='absolute top-2 right-2'>
        <Button
          variant='destructive'
          size='sm'
          onClick={handleDelete}
          className='p-2'
        >
          <Trash2 className='h-4 w-4' />
        </Button>
      </div>
    </div>
  );
}

export default RoomDesignCard;