import { db } from "@/config/db";
import { Users } from "@/config/schema";
import { NextResponse } from "next/server";
import { eq } from "drizzle-orm";

export async function POST(req) {
    try {
        // Get admin email from environment variable
        const adminEmail = process.env.ADMIN_EMAILS;
        
        if (!adminEmail) {
            return NextResponse.json({
                success: false,
                error: 'ADMIN_EMAILS not set in environment'
            }, { status: 400 });
        }

        console.log(`Making ${adminEmail} an admin user...`);

        // Check if user exists
        const userInfo = await db.select().from(Users)
            .where(eq(Users.email, adminEmail));

        if (userInfo.length === 0) {
            return NextResponse.json({
                success: false,
                error: `User with email ${adminEmail} not found. Please sign up first.`
            }, { status: 404 });
        }

        // Update user to admin
        const updatedUser = await db.update(Users)
            .set({ isAdmin: true })
            .where(eq(Users.email, adminEmail))
            .returning({
                id: Users.id,
                name: Users.name,
                email: Users.email,
                isAdmin: Users.isAdmin
            });

        console.log(`Successfully made ${adminEmail} an admin`);

        return NextResponse.json({
            success: true,
            message: `${adminEmail} is now an admin`,
            user: updatedUser[0]
        });

    } catch (error) {
        console.error('Error making user admin:', error);
        return NextResponse.json({
            success: false,
            error: 'Failed to make user admin'
        }, { status: 500 });
    }
}
