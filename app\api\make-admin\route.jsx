import { db } from "@/config/db";
import { Users } from "@/config/schema";
import { NextResponse } from "next/server";
import { eq } from "drizzle-orm";
import { auth } from "@clerk/nextjs/server";

export async function POST(req) {
    try {
        const { userId } = auth();
        if (!userId) {
            return NextResponse.json({ success: false, error: "Unauthorized" }, { status: 401 });
        }

        const currentUser = await db.select().from(Users).where(eq(Users.clerkId, userId));

        if (currentUser.length === 0 || !currentUser[0].isAdmin) {
            return NextResponse.json({ success: false, error: "Forbidden" }, { status: 403 });
        }

        const { emailToMakeAdmin } = await req.json();

        if (!emailToMakeAdmin) {
            return NextResponse.json({ success: false, error: "Email address is required" }, { status: 400 });
        }

        // Check if user exists
        const userInfo = await db.select().from(Users)
            .where(eq(Users.email, emailToMakeAdmin));

        if (userInfo.length === 0) {
            return NextResponse.json({
                success: false,
                error: `User with email ${emailToMakeAdmin} not found. Please sign up first.`
            }, { status: 404 });
        }

        // Update user to admin
        const updatedUser = await db.update(Users)
            .set({ isAdmin: true })
            .where(eq(Users.email, emailToMakeAdmin))
            .returning({
                id: Users.id,
                name: Users.name,
                email: Users.email,
                isAdmin: Users.isAdmin
            });

        console.log(`Successfully made ${emailToMakeAdmin} an admin`);

        return NextResponse.json({
            success: true,
            message: `${emailToMakeAdmin} is now an admin`,
            user: updatedUser[0]
        });

    } catch (error) {
        console.error('Error making user admin:', error);
        return NextResponse.json({
            success: false,
            error: 'Failed to make user admin'
        }, { status: 500 });
    }
}
