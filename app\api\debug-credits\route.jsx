import { db } from "@/config/db";
import { Users } from "@/config/schema";
import { NextResponse } from "next/server";
import { eq } from "drizzle-orm";
import { auth } from "@clerk/nextjs/server";

// Define admin emails - only these users can access admin endpoints
const adminEmails = [
    '<EMAIL>', // Replace with your actual admin email
    // Add more admin emails here if needed
];

async function checkAdminAccess(req) {
    try {
        // Get user email from request headers
        const userEmail = req.headers.get('x-user-email');
        const adminToken = req.headers.get('x-admin-token');

        console.log('Admin access check:', { userEmail, hasToken: !!adminToken });

        // Check if user email is in admin list
        if (userEmail && adminEmails.includes(userEmail)) {
            console.log(`Admin access granted for: ${userEmail}`);
            return { isAdmin: true, userEmail };
        }

        // Fallback: Allow access in development mode with valid token
        if (process.env.NODE_ENV === 'development' && adminToken === 'dev-token') {
            console.log(`Development access granted for: ${userEmail || 'unknown'}`);
            return { isAdmin: true, userEmail: userEmail || 'dev-mode' };
        }

        console.log(`Access denied for: ${userEmail || 'unknown'}`);
        return { isAdmin: false, error: 'Access denied - not an admin user' };
    } catch (error) {
        console.error('Error checking admin access:', error);
        return { isAdmin: false, error: 'Access check failed' };
    }
}

export async function GET(req) {
    // Check admin access
    const { isAdmin, error, userEmail } = await checkAdminAccess(req);

    if (!isAdmin) {
        console.log(`Unauthorized access attempt to debug-credits by: ${userEmail || 'unknown'}`);
        return NextResponse.json({
            success: false,
            error: 'Access denied. Admin privileges required.'
        }, { status: 403 });
    }

    console.log(`Admin access granted to debug-credits for: ${userEmail}`);
    try {
        // Get all users to see the credit distribution
        const allUsers = await db.select().from(Users);
        
        console.log('All users in database:', allUsers);
        
        return NextResponse.json({
            success: true,
            users: allUsers,
            totalUsers: allUsers.length,
            message: 'Credit debug information retrieved successfully'
        });
    } catch (error) {
        console.error('Error in debug-credits:', error);
        return NextResponse.json({
            success: false,
            error: error.message || 'Internal server error'
        }, { status: 500 });
    }
}

export async function POST(req) {
    // Check admin access
    const { isAdmin, error, userEmail: adminEmail } = await checkAdminAccess(req);

    if (!isAdmin) {
        console.log(`Unauthorized access attempt to debug-credits POST by: ${adminEmail || 'unknown'}`);
        return NextResponse.json({
            success: false,
            error: 'Access denied. Admin privileges required.'
        }, { status: 403 });
    }

    console.log(`Admin access granted to debug-credits POST for: ${adminEmail}`);

    try {
        const { action, userEmail, credits } = await req.json();
        
        if (action === 'reset') {
            // Reset all users to 3 credits for testing
            const result = await db.update(Users).set({
                credits: 3
            });
            
            return NextResponse.json({
                success: true,
                message: 'All user credits reset to 3'
            });
        }
        
        if (action === 'set' && userEmail && credits !== undefined) {
            // Set specific user credits
            const result = await db.update(Users).set({
                credits: credits
            }).where(eq(Users.email, userEmail));

            return NextResponse.json({
                success: true,
                message: `Credits set to ${credits} for ${userEmail}`
            });
        }

        if (action === 'cleanup-duplicates') {
            // Find and remove duplicate users, keeping the first one
            const allUsers = await db.select().from(Users);
            const emailMap = new Map();
            const duplicatesToDelete = [];

            // Group users by email and identify duplicates
            allUsers.forEach(user => {
                if (emailMap.has(user.email)) {
                    // This is a duplicate, mark for deletion
                    duplicatesToDelete.push(user.id);
                    console.log(`Found duplicate user: ${user.email} (ID: ${user.id})`);
                } else {
                    // First occurrence, keep it
                    emailMap.set(user.email, user);
                }
            });

            if (duplicatesToDelete.length > 0) {
                // Delete duplicate users
                for (const userId of duplicatesToDelete) {
                    await db.delete(Users).where(eq(Users.id, userId));
                }

                return NextResponse.json({
                    success: true,
                    message: `Removed ${duplicatesToDelete.length} duplicate users`,
                    deletedIds: duplicatesToDelete
                });
            } else {
                return NextResponse.json({
                    success: true,
                    message: 'No duplicate users found'
                });
            }
        }

        return NextResponse.json({
            success: false,
            error: 'Invalid action or missing parameters'
        }, { status: 400 });
        
    } catch (error) {
        console.error('Error in debug-credits POST:', error);
        return NextResponse.json({
            success: false,
            error: error.message || 'Internal server error'
        }, { status: 500 });
    }
}
