"use client";

import Link from "next/link";
import { m, LazyMotion, domAnimation } from "framer-motion";
import { <PERSON><PERSON><PERSON>, Wand2, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Zap } from "lucide-react";
import dynamic from 'next/dynamic';
import HomeSlider from "@/components/HomeSlider";
import { SimpleThemeToggle } from "@/components/ui/theme-toggle";
import Brand<PERSON><PERSON> from "@/components/ui/brand-logo";
import Footer from "@/components/ui/footer";

// Dynamically import the Features component with no SSR
const Features = dynamic(
  () => import('@/components/ui/features'),
  { ssr: false }
);

// Create motion components with proper client-side rendering
const MotionSection = m.section;
const MotionDiv = m.div;
const MotionH1 = m.h1;
const MotionH2 = m.h2;
const MotionP = m.p;

const fadeIn = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.6 } }
};

function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-background/80 dark:from-background dark:to-background/80">
      {/* Floating Background Elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        {[...Array(15)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full bg-primary/10 dark:bg-primary/10"
            style={{
              width: Math.random() * 400 + 100,
              height: Math.random() * 400 + 100,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              filter: 'blur(80px)',
              transform: `rotate(${Math.random() * 360}deg)`
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10">
        {/* Navigation */}
        <header className="absolute w-full z-50">
          <nav className="mx-auto max-w-7xl px-6 py-6 flex items-center justify-between">
            <BrandLogo
              size="large"
              animated={false}
            />
            <div className="hidden md:flex items-center space-x-8">
              <a href="#features" className="text-sm font-medium text-muted-foreground hover:text-primary transition-colors">
                Features
              </a>
              <a href="#how-it-works" className="text-sm font-medium text-muted-foreground hover:text-primary transition-colors">
                How it works
              </a>
              <Link href="/dashboard/buy-credits" className="text-sm font-medium text-muted-foreground hover:text-primary transition-colors">
                Pricing
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <SimpleThemeToggle />
              {/* <Link
                href="/login"
                className="text-sm font-medium text-muted-foreground hover:text-primary transition-colors"
              >
                Sign in
              </Link> */}
              <Link
                href="/dashboard"
                className="px-4 py-2 rounded-lg bg-primary text-primary-foreground text-sm font-medium hover:bg-primary/90 hover:shadow-lg hover:shadow-primary/20 transition-all"
              >
                Get Started
                <ArrowRight className="inline ml-1 h-4 w-4" />
              </Link>
            </div>
          </nav>
        </header>

        {/* Hero Section */}
        <LazyMotion features={domAnimation}>
          <MotionSection 
            initial="hidden"
            animate="visible"
            variants={fadeIn}
            className="relative pt-32 pb-20 sm:pt-40 sm:pb-28 px-6"
          >
          <div className="max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center px-4 sm:px-6 lg:px-8 pt-8 lg:pt-0">
            {/* Left Column - Text Content */}
            <div className="text-center lg:text-left lg:pr-4">
              <MotionDiv
                className="inline-flex items-center px-4 py-1.5 rounded-full bg-primary/10 mb-6"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                <Sparkles className="h-4 w-4 text-primary mr-2" />
                <span className="text-sm font-medium text-primary">
                  AI-Powered Room Redesigns
                </span>
              </MotionDiv>
              <MotionH1
                className="text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight text-foreground"
                variants={fadeIn}
              >
                Transform Your Space with{" "}
                <m.span
                  className="inline-block relative"
                  initial={{ opacity: 0, scale: 0.8, rotateX: -90 }}
                  animate={{
                    opacity: 1,
                    scale: [0.8, 1.1, 1],
                    rotateX: 0,
                  }}
                  transition={{
                    duration: 1.2,
                    delay: 0.5,
                    ease: "easeOut",
                    scale: {
                      times: [0, 0.7, 1],
                      duration: 1.2
                    }
                  }}
                  whileHover={{
                    scale: 1.05,
                    transition: { duration: 0.3 }
                  }}
                  style={{
                    background: "transparent"
                  }}
                >
                  {/* Floating Sparkles */}
                  {[...Array(10)].map((_, i) => (
                    <m.div
                      key={i}
                      className="absolute pointer-events-none z-20"
                      style={{
                        left: `${5 + i * 10}%`,
                        top: `${-20 + (i % 3) * -12}%`,
                      }}
                      initial={{ opacity: 0, scale: 0 }}
                      animate={{
                        opacity: [0, 1, 0.9, 1, 0],
                        scale: [0, 1.8, 1.2, 1.5, 0],
                        y: [0, -35, -15, -30, 0],
                        rotate: [0, 180, 270, 360],
                        x: [0, 15, -8, 12, 0],
                      }}
                      transition={{
                        duration: 1.5,
                        delay: 0.3 + i * 0.15,
                        repeat: Infinity,
                        repeatDelay: 1.2,
                        ease: "easeInOut"
                      }}
                    >
                      <Sparkles
                        className="h-6 w-6 drop-shadow-2xl"
                        style={{
                          filter: "brightness(2) drop-shadow(0 0 12px currentColor) saturate(1.5)",
                          color: i % 3 === 0 ? "#fbbf24" : i % 3 === 1 ? "#f59e0b" : "#06b6d4"
                        }}
                      />
                    </m.div>
                  ))}

                  {/* Additional fast micro sparkles */}
                  {[...Array(6)].map((_, i) => (
                    <m.div
                      key={`micro-${i}`}
                      className="absolute pointer-events-none z-20"
                      style={{
                        left: `${25 + i * 15}%`,
                        top: `${-5 + (i % 2) * -40}%`,
                      }}
                      initial={{ opacity: 0, scale: 0 }}
                      animate={{
                        opacity: [0, 1, 0],
                        scale: [0, 1.2, 0],
                        y: [0, -20, 0],
                        rotate: [0, 720],
                      }}
                      transition={{
                        duration: 1.0,
                        delay: 0.2 + i * 0.12,
                        repeat: Infinity,
                        repeatDelay: 1.8,
                        ease: "easeInOut"
                      }}
                    >
                      <Sparkles
                        className="h-4 w-4"
                        style={{
                          filter: "brightness(2.5) drop-shadow(0 0 8px currentColor)",
                          color: "#ec4899"
                        }}
                      />
                    </m.div>
                  ))}
                  <m.span
                    animate={{
                      scale: [1, 1.02, 1, 0.98, 1],
                      rotateY: [0, 5, 0, -5, 0],
                      color: [
                        "#ff6b6b",
                        "#4ecdc4",
                        "#45b7d1",
                        "#96ceb4",
                        "#feca57",
                        "#ff9ff3",
                        "#54a0ff",
                        "#5f27cd",
                        "#ff6b6b"
                      ]
                    }}
                    transition={{
                      scale: {
                        duration: 4,
                        repeat: Infinity,
                        ease: "easeInOut"
                      },
                      rotateY: {
                        duration: 3,
                        repeat: Infinity,
                        ease: "easeInOut"
                      },
                      color: {
                        duration: 6,
                        repeat: Infinity,
                        ease: "easeInOut"
                      }
                    }}
                    style={{
                      display: "inline-block",
                      fontWeight: "bold",
                      textShadow: "0 0 20px currentColor"
                    }}
                  >
                    AI Magic
                  </m.span>
                </m.span>
              </MotionH1>
              <MotionP
                className="mt-6 text-lg sm:text-xl text-muted-foreground max-w-2xl mx-auto lg:mx-0"
                variants={fadeIn}
                transition={{ delay: 0.1 }}
              >
                Upload a photo of your room and instantly visualize it in any style with our AI-powered design tool. No design skills required.
              </MotionP>
              <MotionDiv 
                className="mt-10 flex flex-col sm:flex-row items-center justify-center lg:justify-start gap-4"
                variants={fadeIn}
                transition={{ delay: 0.2 }}
              >
                <Link
                  href="/dashboard"
                  className="w-full sm:w-auto px-8 py-4 rounded-xl bg-primary hover:bg-primary/90 text-primary-foreground font-medium text-lg shadow-lg hover:shadow-xl hover:shadow-primary/30 transition-all flex items-center justify-center"
                >
                  Redesign Your Room
                  <Wand2 className="ml-2 h-5 w-5" />
                </Link>
                <Link
                  href="#how-it-works"
                  className="w-full sm:w-auto px-8 py-4 rounded-xl bg-secondary hover:bg-secondary/80 text-secondary-foreground font-medium text-lg border border-border shadow-sm hover:shadow transition-all flex items-center justify-center"
                >
                  See How It Works
                </Link>
              </MotionDiv>
            </div>

            {/* Right Column - HomeSlider */}
            <MotionDiv
              className="w-full h-full flex items-center justify-center lg:justify-end mt-8 lg:mt-0"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, ease: "easeOut" }}
            >
              <div className="w-full max-w-xl">
                <HomeSlider />
              </div>
            </MotionDiv>
          </div>
          </MotionSection>
        </LazyMotion>

        {/* Features Showcase */}
        <div id="features" className="pt-12 pb-8">
          <Features />
        </div>

        {/* How It Works */}
        <LazyMotion features={domAnimation}>
          <MotionSection
            className="pt-8 pb-16 bg-muted/50"
            id="how-it-works"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <div className="max-w-7xl mx-auto px-6">
              <div className="text-center mb-16">
                <MotionH2
                  className="text-3xl sm:text-4xl font-bold text-foreground mb-4"
                >
                  How It Works
                </MotionH2>
                <MotionP
                  className="text-lg text-muted-foreground max-w-2xl mx-auto"
                  transition={{ delay: 0.1 }}
                >
                  Transform your space in just a few simple steps
                </MotionP>
              </div>
              <div className="grid md:grid-cols-3 gap-8">
                {[
                  {
                    icon: <Palette className="h-8 w-8 text-primary" />,
                    title: "1. Upload & Select",
                    description: "Upload a photo of your room and select the area you want to redesign."
                },
                {
                  icon: <Wand2 className="h-8 w-8 text-accent" />,
                    title: "2. Choose Style",
                    description: "Browse and select from our curated collection of design styles."
                },
                {
                  icon: <Zap className="h-8 w-8 text-amber-500" />,
                  title: "3. Get Results",
                  description: "Receive multiple AI-generated design options in seconds."
                }
              ].map((feature, index) => (
                <MotionDiv
                  key={index}
                  className="bg-card p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: index * 0.1 }}
                >
                  <div className="w-14 h-14 rounded-xl bg-primary/10 flex items-center justify-center mb-6">
                    {feature.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-card-foreground mb-3">
                    {feature.title}
                  </h3>
                  <p className="text-muted-foreground">
                    {feature.description}
                  </p>
                </MotionDiv>
              ))}
            </div>
            </div>
          </MotionSection>
        </LazyMotion>

        {/* CTA Section */}
        <LazyMotion features={domAnimation}>
          <MotionSection 
            className="py-24 px-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <div className="max-w-4xl mx-auto text-center bg-primary rounded-3xl p-12 relative overflow-hidden">
              <div className="absolute inset-0 bg-grid-white/10" />
              <div className="relative z-10">
                <MotionH2 className="text-3xl sm:text-4xl font-bold text-primary-foreground mb-6">
                  Ready to transform your space?
                </MotionH2>
                <MotionP
                  className="text-lg text-primary-foreground/80 mb-8 max-w-2xl mx-auto"
                  transition={{ delay: 0.1 }}
                >
                  Join thousands of happy users who&apos;ve already redesigned their rooms with our AI-powered tool.
                </MotionP>
                <MotionDiv
                  whileHover={{ scale: 1.03 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Link
                    href="/dashboard"
                    className="inline-flex items-center justify-center px-8 py-4 rounded-xl bg-background text-foreground font-medium text-lg shadow-lg hover:shadow-xl hover:shadow-primary/30 transition-all"
                  >
                    Get Started for Free
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </MotionDiv>
              </div>
            </div>
          </MotionSection>
        </LazyMotion>
      </div>

      {/* Footer */}
      <Footer />
    </div>
  );
}

export default Home;
