import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/config/db';
import { AiGeneratedImage } from '@/config/schema';
import { eq } from 'drizzle-orm';
import { storage } from '@/config/firebaseConfig';
import { ref, deleteObject } from 'firebase/storage';

export async function POST(req) {
  try {
    const { userId } = auth();
    if (!userId) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    const { id } = await req.json();

    const imageResult = await db.select().from(AiGeneratedImage).where(eq(AiGeneratedImage.id, id));

    if (imageResult.length === 0) {
        return new NextResponse('Image not found', { status: 404 });
    }

    const image = imageResult[0];

    // Delete images from Firebase Storage
    const deleteImageFromStorage = async (imageUrl) => {
        if (!imageUrl) return;
        try {
            const imageRef = ref(storage, imageUrl);
            await deleteObject(imageRef);
        } catch (error) {
            // Log the error but don't block the database deletion
            console.error('Error deleting image from storage:', error);
        }
    };

    await deleteImageFromStorage(image.aiImage);
    await deleteImageFromStorage(image.orgImage);

    await db.delete(AiGeneratedImage).where(eq(AiGeneratedImage.id, id));

    return new NextResponse(JSON.stringify({ message: 'Image and record deleted successfully' }), { status: 200 });
  } catch (error) {
    console.error('Error deleting image:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
