'use client'
import { UserButton } from '@clerk/nextjs'
import React, { useContext } from 'react'
import Listing from './_components/Listing'
import CleanupTool from './_components/CleanupTool'
import { UserDetailContext } from '@/app/_context/UserDetailContext'

function Dashboard() {
  const { userDetail } = useContext(UserDetailContext)

  // Define admin emails - only these users can see admin tools
  const adminEmails = [
    '<EMAIL>', // Your email - replace with your actual admin email
    // Add more admin emails here if needed
  ]

  const isAdmin = userDetail?.email && adminEmails.includes(userDetail.email)

  return (
    <div>
        <Listing/>

        {/* Admin Tools - Only show to specific admin users */}
        {isAdmin && (
          <div className="mt-8 p-4 border-t border-red-200 bg-red-50">
            <div className="flex items-center gap-2 mb-4">
              <span className="text-red-600">🔒</span>
              <h2 className="text-lg font-semibold text-red-800">Admin Tools</h2>
              <span className="text-xs bg-red-200 text-red-800 px-2 py-1 rounded">
                Admin Only
              </span>
            </div>
            <CleanupTool />
          </div>
        )}
    </div>
  )
}

export default Dashboard