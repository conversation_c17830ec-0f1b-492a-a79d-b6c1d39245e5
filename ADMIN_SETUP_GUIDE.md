# 🔐 Database-Driven Admin System Setup Guide

Your admin system has been upgraded from email-based to database-driven for better security and management!

## 🚀 What Changed

### ✅ **Before (Email-based)**
- Admin access controlled by hardcoded email lists
- Had to update code in multiple files to add/remove admins
- Less secure and harder to manage

### ✅ **After (Database-driven)**
- Admin status stored in database (`isAdmin` field)
- Centralized admin management through API
- More secure and easier to manage
- No code changes needed to promote/demote users

## 🛠️ Setup Instructions

### 1. **Add Super Admin Key to Environment**

Add this to your `.env.local` file:
```env
SUPER_ADMIN_KEY=your-super-secret-admin-key-here
```

⚠️ **Important**: Choose a strong, unique key and keep it secret!

### 2. **Promote Your First Admin User**

#### Option A: Using the Script (Recommended)
```bash
# Make sure your Next.js server is running
npm run dev

# In another terminal, run the promotion script
node scripts/promote-admin.js <EMAIL>
```

#### Option B: Using API Directly
```bash
curl -X POST http://localhost:3000/api/admin-management \
  -H "Content-Type: application/json" \
  -H "x-super-admin-key: your-super-secret-admin-key-here" \
  -d '{"email": "<EMAIL>", "isAdmin": true}'
```

### 3. **Verify Admin Access**

1. Sign in to your application with the promoted email
2. Go to the dashboard
3. You should see the admin tools section at the bottom

## 🔧 Managing Admins

### **Promote User to Admin**
```bash
node scripts/promote-admin.js <EMAIL>
```

### **Demote Admin to Regular User**
```bash
curl -X POST http://localhost:3000/api/admin-management \
  -H "Content-Type: application/json" \
  -H "x-super-admin-key: your-super-secret-admin-key-here" \
  -d '{"email": "<EMAIL>", "isAdmin": false}'
```

### **List All Admin Users**
```bash
curl -X GET http://localhost:3000/api/admin-management \
  -H "x-super-admin-key: your-super-secret-admin-key-here"
```

## 🛡️ Security Features

### **Database Schema**
- `isAdmin` field added to Users table
- Defaults to `false` for new users
- Boolean field for clear true/false status

### **API Protection**
- Super admin key required for admin management
- Database queries verify user existence
- Proper error handling and logging

### **Frontend Protection**
- Dashboard checks `userDetail.isAdmin` from database
- No hardcoded email lists
- Clean separation of admin/user interfaces

## 📍 File Changes Made

### **Database**
- ✅ `config/schema.js` - Added `isAdmin` field
- ✅ Migration generated and applied

### **API Routes**
- ✅ `app/api/verify-user/route.jsx` - Returns `isAdmin` field
- ✅ `app/api/debug-credits/route.jsx` - Uses database admin check
- ✅ `app/api/cleanup-duplicates/route.jsx` - Uses database admin check
- ✅ `app/api/admin-management/route.jsx` - New admin management endpoint

### **Frontend**
- ✅ `app/dashboard/page.jsx` - Uses `userDetail.isAdmin`

### **Utilities**
- ✅ `scripts/promote-admin.js` - Admin promotion script

## 🚨 Important Notes

### **First Time Setup**
1. The database migration has been applied
2. All existing users have `isAdmin = false`
3. You MUST promote at least one user to admin using the script
4. Without an admin user, no one can access admin tools

### **Security Best Practices**
- Keep your `SUPER_ADMIN_KEY` secret and secure
- Only use the admin management API from trusted environments
- Regularly audit your admin users
- Consider rotating the super admin key periodically

### **Troubleshooting**
- If admin tools don't appear, check browser console for errors
- Verify the user has `isAdmin: true` in the database
- Ensure the user context is properly loaded
- Check that the database migration was applied successfully

## 🎉 Benefits

- ✅ **More Secure**: No hardcoded credentials in code
- ✅ **Easier Management**: Promote/demote users without code changes
- ✅ **Better Scalability**: Support for multiple admins
- ✅ **Audit Trail**: Database records of admin status changes
- ✅ **Flexible**: Easy to extend with roles and permissions later

Your admin system is now production-ready and much more secure! 🚀
