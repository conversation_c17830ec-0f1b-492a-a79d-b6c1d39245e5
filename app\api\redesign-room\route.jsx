import { db } from "@/config/db";
import { storage } from "@/config/firebaseConfig";
import { AiGeneratedImage } from "@/config/schema";
import { useUser } from "@clerk/nextjs";
import axios from "axios";
import { getDownloadURL, ref, uploadString } from "firebase/storage";
import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";
import Replicate from "replicate";

const replicate = new Replicate({
    auth:process.env.NEXT_PUBLIC_REPLICATE_API_TOKEN
});

// Debug: Check if API token is loaded
console.log('Replicate API token loaded:', !!process.env.NEXT_PUBLIC_REPLICATE_API_TOKEN);

// Function to validate image URL and format
async function validateImageUrl(imageUrl) {
    try {
        const response = await axios.head(imageUrl);
        const contentType = response.headers['content-type'];
        const contentLength = response.headers['content-length'];

        console.log('Image validation:', {
            url: imageUrl,
            contentType,
            contentLength,
            status: response.status
        });

        // Check if it's a valid image type
        const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        if (!validTypes.includes(contentType)) {
            throw new Error(`Invalid image type: ${contentType}`);
        }

        return { contentType, contentLength };
    } catch (error) {
        console.error('Image validation failed:', error);
        throw error;
    }
}

// Function to process PNG images for better AI model compatibility
async function processPngForAI(imageUrl) {
    try {
        console.log('Processing PNG for AI compatibility...');

        // For now, we'll try a different approach - use the original URL but with specific handling
        // The issue might be with PNG transparency or color channels

        // Download the image to check its properties
        const response = await axios.get(imageUrl, { responseType: 'arraybuffer' });
        const imageBuffer = Buffer.from(response.data);

        console.log('PNG image size:', imageBuffer.length);

        // Create a new PNG without alpha channel by converting to RGB
        // This is a simplified approach - in production you'd use a proper image library
        const base64Image = imageBuffer.toString('base64');
        const pngDataUrl = `data:image/png;base64,${base64Image}`;

        // For now, return the original URL but log the processing attempt
        console.log('PNG processed for AI compatibility');
        return imageUrl;

    } catch (error) {
        console.error('Error processing PNG:', error);
        // If processing fails, return original URL
        return imageUrl;
    }
}

export async function POST(req){
    const { userId } = auth();
    if (!userId) {
      return new Response("Unauthorized", { status: 401 });
    }

    const {imageUrl,roomType,designType,mood,roomFeatures,additionalReq,userEmail}=await req.json();

    // Debug: Log received data
    console.log('Received data:', {
        imageUrl,
        roomType,
        designType,
        mood,
        roomFeatures,
        additionalReq,
        userEmail
    });

    // Convert Image to AI Image

    try{
        // Validate image URL
        if (!imageUrl) {
            console.error('No image URL provided');
            return NextResponse.json({error: 'Image URL is required'}, {status: 400});
        }

        console.log('Processing image URL:', imageUrl);

        // Validate the image
        const imageInfo = await validateImageUrl(imageUrl);

        // For PNG files, we need to handle them differently
        // The client-side conversion should handle this, but as a fallback,
        // we'll use the original URL and let the user know to convert PNG to JPEG
        let processedImageUrl = imageUrl;
        if (imageInfo.contentType === 'image/png') {
            console.log('PNG detected - this may cause issues with the AI model');
            console.log('Recommendation: Convert PNG to JPEG on the client side');
            // For now, we'll try with the original PNG URL
            processedImageUrl = imageUrl;
        }
        // Build mood string from array
        const moodString = mood && mood.length > 0 ? mood.join(', ') : '';
        const moodPrompt = moodString ? ` with a ${moodString} atmosphere` : '';

        // Build room features string from array
        const featuresString = roomFeatures && roomFeatures.length > 0 ? roomFeatures.join(', ') : '';
        const featuresPrompt = featuresString ? ` featuring ${featuresString}` : '';

        // Clean up additional requirements
        const additionalPrompt = additionalReq && additionalReq.trim() ? ` ${additionalReq.trim()}` : '';

        // Construct the final prompt
        const finalPrompt = `A ${roomType || 'room'} with a ${designType || 'modern'} style interior${moodPrompt}${featuresPrompt}${additionalPrompt}`;

        console.log('Generated prompt:', finalPrompt);

        const input = {
            image: processedImageUrl,
            prompt: finalPrompt,
            // Add parameters that might help with PNG processing
            num_inference_steps: 20,
            guidance_scale: 7.5,
            strength: 0.8
        };

        console.log('Sending to Replicate API:', input);
        console.log('Original image type:', imageInfo.contentType);

        const output = await replicate.run("adirik/interior-design:76604baddc85b1b4616e1c6475eca080da339c8875bd4996705440484a6eac38", { input });

        //const output = await replicate.run("black-forest-labs/flux-kontext-dev", { input });


        console.log('Replicate API output:', output);
        console.log('Output type:', typeof output);
        console.log('Output is array:', Array.isArray(output));

        if (!output) {
            console.error('No output from Replicate API');
            return NextResponse.json({error: 'Failed to generate AI image'}, {status: 500});
        }

        // Handle different output formats
        let outputUrl = output;
        if (Array.isArray(output) && output.length > 0) {
            outputUrl = output[0];
            console.log('Using first output from array:', outputUrl);
        }

        // Convert Output Url to BASE64 Image
        const base64Image=await ConvertImageToBase64(outputUrl);
        // Save Base64 to Firebase
        const fileName=Date.now()+'.png';
        const storageRef=ref(storage,'room-redesign/'+fileName);
        await uploadString(storageRef,base64Image,'data_url');
        const downloadUrl=await getDownloadURL(storageRef);
        console.log('Generated image URL:', downloadUrl);
        // Save All to Database

        const dbResult=await db.insert(AiGeneratedImage).values({
            roomType:roomType,
            designType:designType,
            orgImage:imageUrl,
            aiImage:downloadUrl,
            userEmail:userEmail
        }).returning({id:AiGeneratedImage.id});
        console.log(dbResult);
         return NextResponse.json({'result':downloadUrl});
        
    }catch(e){
        console.error('Error in redesign-room API:', e);
        return NextResponse.json({
            error: e.message || 'Internal server error',
            details: e.toString()
        }, {status: 500});

    }
  

}

async function ConvertImageToBase64(imageUrl){
    try {
        console.log('Converting image to base64:', imageUrl);

        const resp=await axios.get(imageUrl,{responseType:'arraybuffer'});

        console.log('Image response headers:', resp.headers);
        console.log('Image response status:', resp.status);
        console.log('Image data size:', resp.data.byteLength);

        const base64ImageRaw=Buffer.from(resp.data).toString('base64');
        const mimeType = resp.headers['content-type'] || 'image/png';

        console.log('Detected MIME type:', mimeType);
        console.log('Base64 length:', base64ImageRaw.length);

        return `data:${mimeType};base64,${base64ImageRaw}`;
    } catch (error) {
        console.error('Error converting image to base64:', error);
        throw error;
    }
}