import React, { useState, useImperativeHandle, forwardRef, useEffect, useCallback } from 'react'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import { cn } from "@/lib/utils"
import { AlertCircle } from "lucide-react"

const RoomType = forwardRef(function RoomType({ value, selectedRoomType, onValidationChange }, ref) {
    const [error, setError] = useState("")
    const [touched, setTouched] = useState(false)

    // Set default value on component mount
    const validateSelection = useCallback((currentValue) => {
        if (!currentValue || currentValue.trim() === "") {
            setError("Please select a room type")
            onValidationChange?.(false)
            return false
        }
        setError("")
        onValidationChange?.(true)
        return true
    }, [onValidationChange]);

    // Expose validation method to parent component
    useImperativeHandle(ref, () => ({
        validate: () => {
            const isValid = validateSelection(value)
            setTouched(true)
            return isValid
        },
        reset: () => {
            // Parent will handle resetting the value
            setError("")
            setTouched(false)
        },
        getValue: () => value
    }))

    

    const handleValueChange = (newValue) => {
        setTouched(true)
        validateSelection(newValue)
        selectedRoomType(newValue)
    }

    const handleOpenChange = (open) => {
        // Only show error if user has interacted and closed without selecting
        if (!open && touched && !value) {
            setError("Please select a room type")
            onValidationChange?.(false)
        }
    }

    const hasError = error && touched

    return (
        <div className="space-y-2">
            <label className={cn(
                "text-sm font-medium",
                hasError ? "text-red-600" : "text-muted-foreground"
            )}>
                Room Type *
            </label>
            <Select
                value={value}
                onValueChange={handleValueChange}
                onOpenChange={handleOpenChange}
            >
                <SelectTrigger
                    className={cn(
                        "w-full transition-colors",
                        hasError && [
                            "border-red-500 focus:border-red-500 focus:ring-red-500/20",
                            "bg-red-50 dark:bg-red-950/10"
                        ]
                    )}
                    aria-invalid={hasError}
                    aria-describedby={hasError ? "room-type-error" : undefined}
                >
                    <SelectValue placeholder="Choose a room type..." />
                </SelectTrigger>
                <SelectContent className="max-h-[300px]">
                    <SelectItem value="Living Room">Living Room</SelectItem>
                    <SelectItem value="Open Kitchen">Open Kitchen</SelectItem>
                    <SelectItem value="Bedroom">Bedroom</SelectItem>
                    <SelectItem value="Bathroom">Bathroom</SelectItem>
                    <SelectItem value="Kitchen">Kitchen</SelectItem>
                    <SelectItem value="Dining Room">Dining Room</SelectItem>
                    <SelectItem value="Attic">Attic</SelectItem>
                    <SelectItem value="Study Room">Study Room</SelectItem>
                    <SelectItem value="Home Office">Home Office</SelectItem>
                    <SelectItem value="Family Room">Family Room</SelectItem>
                    <SelectItem value="Formal Dining Room">Formal Dining Room</SelectItem>
                    <SelectItem value="Kids Room">Kids Room</SelectItem>
                    <SelectItem value="Balcony">Balcony</SelectItem>
                    <SelectItem value="Gaming Room">Gaming Room</SelectItem>
                    <SelectItem value="Meeting Room">Meeting Room</SelectItem>
                    <SelectItem value="Workshop">Workshop</SelectItem>
                    <SelectItem value="Fitness Gym">Fitness Gym</SelectItem>
                    <SelectItem value="Coffee Shop">Coffee Shop</SelectItem>
                    <SelectItem value="Clothing Store">Clothing Store</SelectItem>
                    <SelectItem value="Restaurant">Restaurant</SelectItem>
                    <SelectItem value="Office">Office</SelectItem>
                    <SelectItem value="Co-Working Space">Co-Working Space</SelectItem>
                    <SelectItem value="Hotel Lobby">Hotel Lobby</SelectItem>
                    <SelectItem value="Hotel Room">Hotel Room</SelectItem>
                    <SelectItem value="Hotel Bathroom">Hotel Bathroom</SelectItem>
                    <SelectItem value="Exhibition Space">Exhibition Space</SelectItem>
                    <SelectItem value="Onsen">Onsen</SelectItem>
                    <SelectItem value="Working Space">Working Space</SelectItem>
                    <SelectItem value="Entryway">Entryway</SelectItem>
                    <SelectItem value="Mudroom">Mudroom</SelectItem>
                    <SelectItem value="Hallway">Hallway</SelectItem>
                    <SelectItem value="Walk-in Closet">Walk-in Closet</SelectItem>
                    <SelectItem value="Pantry">Pantry</SelectItem>
                    <SelectItem value="Sunroom">Sunroom</SelectItem>
                    <SelectItem value="Laundry Room">Laundry Room</SelectItem>
                    <SelectItem value="Library">Library</SelectItem>
                    <SelectItem value="Playroom">Playroom</SelectItem>
                    <SelectItem value="Guest Room">Guest Room</SelectItem>
                    <SelectItem value="Basement">Basement</SelectItem>
                    <SelectItem value="Loft">Loft</SelectItem>
                    <SelectItem value="Nursery">Nursery</SelectItem>
                    <SelectItem value="Media Room">Media Room</SelectItem>
                    <SelectItem value="Home Theater">Home Theater</SelectItem>
                    <SelectItem value="Bar">Bar</SelectItem>
                    <SelectItem value="Wine Cellar">Wine Cellar</SelectItem>
                    <SelectItem value="Patio">Patio</SelectItem>
                    <SelectItem value="Terrace">Terrace</SelectItem>
                    <SelectItem value="Garden Room">Garden Room</SelectItem>
                    <SelectItem value="Garage">Garage</SelectItem>
                    <SelectItem value="Staircase Landing">Staircase Landing</SelectItem>
                </SelectContent>
            </Select>

            {/* Error Message */}
            {hasError && (
                <div
                    id="room-type-error"
                    className="flex items-center gap-2 text-sm text-red-600"
                    role="alert"
                    aria-live="polite"
                >
                    <AlertCircle className="h-4 w-4 flex-shrink-0" />
                    <span>{error}</span>
                </div>
            )}

            {/* Success Indicator */}
            {value && !hasError && touched && (
                <div className="flex items-center gap-2 text-sm text-primary">
                    <div className="h-4 w-4 rounded-full bg-primary/10 flex items-center justify-center">
                        <div className="h-2 w-2 rounded-full bg-primary"></div>
                    </div>
                    <span>Room type selected</span>
                </div>
            )}
        </div>
    )
})

export default RoomType
