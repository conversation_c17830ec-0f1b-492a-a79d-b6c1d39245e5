import { db } from "@/config/db";
import { Users } from "@/config/schema";
import { eq } from "drizzle-orm";
import { NextResponse } from "next/server";

export async function POST(req){
    const {user}=await req.json();

    try{
        const userEmail = user?.primaryEmailAddress?.emailAddress;

        if (!userEmail) {
            console.error('No email provided in user verification');
            return NextResponse.json({error: 'Email is required'}, {status: 400});
        }

        console.log('Verifying user:', userEmail);

        //If User Already Exist?
        const userInfo=await db.select().from(Users)
        .where(eq(Users.email, userEmail))
        console.log("User found in DB:",userInfo);

        //If Not Will Add New User to DB
        if(userInfo?.length==0)
        {
            console.log('Creating new user with default credits');

            try {
                const SaveResult=await db.insert(Users)
                .values({
                    name:user?.fullName,
                    email:userEmail,
                    imageUrl:user?.imageUrl,
                    credits: 3, // Explicitly set default credits
                    isAdmin: false // Default to non-admin user
                }).returning({
                    id: Users.id,
                    name: Users.name,
                    email: Users.email,
                    imageUrl: Users.imageUrl,
                    credits: Users.credits,
                    isAdmin: Users.isAdmin
                })

                console.log('New user created:', SaveResult[0]);
                return NextResponse.json({'result':SaveResult[0]})
            } catch (insertError) {
                // Handle potential duplicate key error (race condition)
                if (insertError.message?.includes('duplicate') || insertError.code === '23505') {
                    console.log('Duplicate user detected during insert, fetching existing user');

                    // Fetch the existing user that was created by another request
                    const existingUser = await db.select().from(Users)
                    .where(eq(Users.email, userEmail))

                    if (existingUser.length > 0) {
                        console.log('Returning existing user after duplicate detection:', existingUser[0]);
                        return NextResponse.json({'result': existingUser[0]})
                    }
                }

                // Re-throw if it's not a duplicate error
                throw insertError;
            }
        }

        console.log('Returning existing user:', userInfo[0]);
        return NextResponse.json({'result':userInfo[0]})
    }
    catch(e){
        console.error('Error in verify-user:', e);
        return NextResponse.json({error:e.message || 'Internal server error'}, {status: 500})
    }
}