# AI Room Redesign


This is a Next.js application that allows users to redesign their rooms using AI. Users can upload a photo of their room, select a style, and get AI-generated design options.



You are my pair‑programmer.

Goal
-----
Implement the feature described by the human user with the MINIMUM necessary changes, keeping the codebase idiomatic and easy to review.

High‑level workflow
-------------------
1. Clarify requirements (ask concise follow‑ups only if blocking).
2. **Context scan**  
   – List all files you must read before coding.  
   – For each file, skim → then open/quote only the relevant slices (line numbers).  
   – Identify existing patterns (auth helpers, db utils, UI libs, env variables).  
3. **Plan**  
   – Draft a step‑by‑step task list (add / edit / delete files, migrations, tests, docs).  
   – Mark each task as `💾 code`, `🧪 test`, `📄 doc`, or `⚙️ infra`.  
4. **Implement** (iterate until done)  
   For each task:  
   a. Show the *diff* you intend to apply (small, focused).  
   b. Apply it.  
   c. Run/build/test; capture output.  
   d. If errors, debug; otherwise mark the task ✔️.  
5. **Self‑check**  
   – Run linters, type‑checkers, unit tests.  
   – Grep for TODO / FIXME left behind.  
6. **Summarize**  
   – Bullet the finished work (schema, API, UI, scripts).  
   – Give exact follow‑up steps a teammate must do (e.g., “run npx drizzle-kit push”).  

Rules
-----
* **No magic numbers / strings** in new code—promote to named constants.
* Prefer existing abstractions over new ones.
* Do not introduce new external packages without explicit permission.
* Keep migrations idempotent and timestamp‑based.
* All new public functions **must** be typed / documented.
* After major edits, update any affected README / .env.example.

Formatting
----------
* Wrap code blocks in triple back‑ticks with language hint.  
* Prefix code‑diffs with:  
  ```diff
  --- a/path/file.ts
  +++ b/path/file.ts
  @@


## Core Development Principles & AI Directives

These principles are paramount. All new features, modifications, and code generations must strictly adhere to them.

### 1. Functional Stability & Non-Disturbance

- **Prioritize existing functionality:** Any change must *not* break or negatively impact already working features.
- **Modularity:** Ensure new code is isolated, focused, and loosely coupled with existing systems. Prefer adding new files/components over heavily modifying existing ones if it helps maintain clarity and prevent regressions.
- **Clear Separation of Concerns:** Logic should be clearly separated (e.g., UI, data fetching, business logic, API handling).
- **Idempotency:** Operations should ideally be idempotent where applicable (e.g., API calls that can be safely retried).

### 2. Mandatory Security Protocols

Security is not optional. All code changes must adhere to the following protocols to protect user data and system integrity. A violation of these rules is considered a critical failure.

#### **A. API Route Security Checklist**

Before completing any change to a file in `app/api/`, you **must** verify that the code adheres to this checklist:

1.  **Verify Authentication:** The route must immediately check for a valid, authenticated user session. Use Clerk's `auth()` or `getAuth()` functions at the beginning of the request handler. Reject any request without a valid session.
    ```javascript
    // Example:
    import { auth } from '@clerk/nextjs/server';
    const { userId } = auth();
    if (!userId) {
      return new Response("Unauthorized", { status: 401 });
    }
    ```

2.  **Validate Input:** Never trust user input. All incoming data from the request body (`req.json()`) or URL parameters must be rigorously validated. Check for correct types, lengths, and formats. Reject any request with invalid data.

3.  **Check Authorization:** Authentication is not enough. Verify that the authenticated user has the specific permissions required to perform the requested action. For example, ensure a user can only access or modify their *own* data.
    ```javascript
    // Example:
    const design = await db.select().from(designs).where(eq(designs.id, designId));
    if (design.userId !== userId) {
      return new Response("Forbidden", { status: 403 });
    }
    ```

4.  **Use Parameterized Queries:** To prevent SQL injection, **all** database queries must be performed using the Drizzle ORM with its built-in query builder and parameterization. Never construct SQL queries using string concatenation with user input.

5.  **Secure Error Handling:** Wrap all logic in `try...catch` blocks. In the `catch` block, log the detailed error for debugging but return a generic, non-descriptive error message to the client to avoid leaking implementation details.

#### **B. Frontend Security**

1.  **Protect Routes & Components:** Use Clerk's `<Protect>` component or the `useAuth` hook to secure pages and components, ensuring they are only accessible to authenticated users with the correct roles or permissions.
2.  **Avoid Exposing Sensitive Data:** Be vigilant about what data is passed from Server Components to Client Components. Ensure that no sensitive information (API keys, internal user IDs, etc.) is ever exposed in the client-side props.

#### **C. Secrets Management**

-   **Never Hardcode Secrets:** Under no circumstances should API keys, database connection strings, or any other secrets be hardcoded in the source code.
-   **Use Environment Variables:** All secrets **must** be stored in the `.env.local` file and accessed via `process.env`. This file is explicitly ignored by Git and must never be committed.

#### **C. Secrets Management**

-   **Never Hardcode Secrets:** Under no circumstances should API keys, database connection strings, or any other secrets be hardcoded in the source code.
-   **Use Environment Variables:** All secrets **must** be stored in the `.env.local` file and accessed via `process.env`. This file is explicitly ignored by Git and must never be committed.

### 3. Quality Assurance & Bug Prevention

- **Robustness:** Write resilient code that handles expected and unexpected edge cases gracefully.
- **Comprehensive Error Handling:** All asynchronous operations and API interactions (both client and server) must be wrapped in `try...catch` blocks. Provide clear, actionable error messages for logging and user feedback.
- **Maintainability & Readability:** Code should be easy to understand, debug, and maintain by other developers. Follow consistent patterns.
- **Testability:** Structure code in a way that facilitates future unit, integration, and end-to-end testing (even if tests are not immediately generated).
- **Self-Correction & Review:** Before proposing any changes, thoroughly self-evaluate for potential technical or functional bugs based on the above principles.

### 4. Change Verification & Regression Prevention

To ensure the stability of the application, every code change, without exception, must be validated against the following protocol. This is the primary mechanism for preventing regressions.

1.  **Analyze Impact Before Changing:**
    -   Before modifying any function, component, or utility, you **must** use `search_file_content` to find all its usages across the codebase.
    -   Carefully analyze these usages to understand the potential impact of your changes. Do not proceed if a change will break existing functionality.

2.  **Prioritize Isolation for New Features:**
    -   When adding new functionality, it is strongly preferred to create new, isolated files or components rather than making extensive changes to existing, complex ones.
    -   This minimizes the risk of unintended side effects and makes the new code easier to understand and maintain.

3.  **Mandatory Build & Lint Verification:**
    -   After every single code modification (e.g., using `replace` or `write_file`), you **must** immediately run the following commands to verify the integrity of the application:
        **[REMOVED - See AI Operational Mode below]**
    -   **A successful `npm run build` is mandatory.** It confirms that the project still compiles without syntax or type errors. If the build fails, the change is invalid and must be corrected. **[MODIFIED - See AI Operational Mode below]**
    -   **A successful `npm run lint` is mandatory.** It ensures the change adheres to the project's coding standards. **[MODIFIED - See AI Operational Mode below]**

This multi-step verification process is not optional; it is a core requirement for maintaining a healthy and functional codebase.

### 5. Next.js Architectural Principles & Mandates

### 6. AI Operational Mode (User-Driven Verification)

To facilitate a smoother, user-driven development experience, the AI will operate under the following mode:
-   **Code Changes & Saving:** The AI will make requested code modifications and save the files.
-   **No Automatic Builds/Linting:** The AI will *not* automatically run `npm run build` or `npm run lint` after every code change.
-   **User Verification:** It is the user's responsibility to run `npm run dev` (or their preferred development server command) and refresh the browser to observe and test the changes.
-   **Explicit Build Requests:** The AI will only execute `npm run build` or `npm run lint` when explicitly requested by the user (e.g., for production builds or final verification).
-   **Environment Debugging:** If the user encounters environment issues (e.g., "Cannot find module" errors, or changes not reflecting), they should inform the AI, and the AI will then assist in debugging those specific problems.

To ensure a consistent, performant, and maintainable codebase, all development must adhere to these architectural principles.

To ensure a consistent, performant, and maintainable codebase, all development must adhere to these architectural principles. These are the guiding philosophies for how we build features in this specific Next.js application.

#### **A. Data Fetching & Caching Strategy**

-   **Mandate: Server Components are the Default.** The primary method for data fetching is through React Server Components (RSCs) using the native `fetch` API. This is the default approach as it leverages Next.js's built-in server-side rendering and caching capabilities, leading to better performance and reduced client-side load.
-   **Rule: Define Explicit Caching Policies.** For dynamic data that changes over time, you must define a revalidation strategy. Use the `{ next: { revalidate: <seconds> } }` option in your `fetch` calls to control how often data is refreshed.
-   **Guideline: Use Client-Side Fetching Sparingly.** Client-side data fetching (e.g., using `useEffect` and `useState` with `axios` or `fetch`) should only be used for data that is highly dynamic and dependent on immediate user interaction (e.g., a search bar with live results). For other cases, prefer passing data as props from a Server Component.

#### **B. State Management Philosophy**

-   **Mandate: Use Existing Context Providers.** For global or cross-component state, you **must** utilize the existing React Context providers, such as `ThemeContext` and `UserDetailContext`. Before creating new state mechanisms, always check if the required state can be managed by an existing provider.
-   **Rule: No Unauthorized State Libraries.** Do not introduce new third-party state management libraries (e.g., Redux, Zustand, Jotai) without explicit user permission. The goal is to maintain a minimal and consistent state management footprint.
-   **Guideline: Encapsulate New Global State.** If a new, distinct domain of global state is required, it must be encapsulated within a new, well-named Context provider. This provider should be added to the `app/_context` directory and integrated into the main `app/provider.js` file.

#### **C. Image Optimization Mandate**

-   **Mandate: Use `next/image` for All Images.** To ensure optimal performance, all images displayed in the application **must** be rendered using the `<Image>` component from `next/image`. This provides automatic optimization, lazy loading, responsive scaling, and modern format delivery (e.g., WebP).
-   **Rule: Reference Public Images Correctly.** All images stored in the `/public` directory must be referenced with a leading slash in the `src` prop (e.g., `src="/logo.svg"`). Do not use relative paths for public assets.

### AI Self-Correction & Operational Learnings

This section documents specific operational learnings and self-corrections to improve future AI performance and prevent recurring mistakes. These are direct mandates for the AI's behavior.

#### **1. Platform-Specific Commands**

-   **Learning:** Commands like `rm -rf` are Unix-specific and will fail on Windows systems. Assuming the operating system can lead to errors.
-   **Mandate:** Before executing any file system modification command (e.g., `rm`, `cp`, `mv`), the AI **must** verify the user's operating system. If the OS is Windows, use the appropriate Windows-native commands (e.g., `rmdir /s /q` for directory deletion).

#### **2. Interactive Shell Commands & Linter Setup**

-   **Learning:** Interactive shell commands (e.g., `npm run lint` when it prompts for configuration) cause the AI to get stuck or fail. Additionally, assuming a linter is installed or configured when it's not can lead to a loop of errors.
-   **Mandate:**
    -   **Avoid Interactive Commands:** The AI **must** avoid executing shell commands that are known to be interactive or require user input during execution. If an interactive command is the only way to achieve a task, the AI must inform the user and suggest a manual step or an alternative non-interactive approach.
    -   **Linter Setup:** If a linter is not set up or is misconfigured, the AI **must not** attempt to interact with prompts. Instead, it should propose a non-interactive setup, such as:
        -   Directly writing the `.eslintrc.json` (or equivalent) file with a standard configuration.
        -   Explicitly installing necessary linter packages (e.g., `npm install --save-dev eslint`).
        -   Clearly communicating the steps to the user.

#### **3. Duplicate Import Prevention**

-   **Learning:** When using the `replace` tool, it is easy to inadvertently introduce duplicate import statements if the `old_string` and `new_string` are not meticulously crafted, leading to build failures.
-   **Mandate:** Before executing a `replace` operation that involves modifying import sections, the AI **must** carefully review the `old_string` and `new_string` to ensure:
    -   No existing import statements are duplicated.
    -   The `old_string` accurately captures the context to avoid unintended changes.
    -   The `new_string` integrates seamlessly without introducing syntax errors or redundant code.
    -   Prioritize modifying existing import lines rather than adding new, identical ones if the module is already imported.

---

## Tech Stack

- **Framework:** Next.js
- **Authentication:** Clerk
- **Database:** Neon (PostgreSQL) with Drizzle ORM
- **Styling:** Tailwind CSS with shadcn/ui components
- **AI Model:** Replicate
- **Deployment:** Vercel

## Getting Started

1.  **Install dependencies:**
    ```bash
    npm install
    ```
2.  **Set up environment variables:**
    Create a `.env.local` file by copying the `.env.example` file and fill in the required environment variables.
3.  **Run the development server:**
    ```bash
    npm run dev
    ```
    Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Key Features

- User authentication with Clerk
- AI-powered room redesign using the Replicate API
- View and manage redesigned rooms in a dashboard
- Purchase credits to redesign more rooms
- Dark and light mode support

## Project Structure

The project follows the Next.js App Router structure.

-   `app/`: This is the core of the application.
    -   `layout.js`: The root layout for the entire application.
    -   `page.js`: The main landing page.
    -   `(auth)`: A route group for authentication-related pages like sign-in and sign-up, using Clerk.
    -   `dashboard/`: A protected route group for the user dashboard.
        -   `layout.jsx`: A specific layout for the dashboard section.
        -   `page.jsx`: The main dashboard page.
        -   `_components/`: Private components specific to the dashboard routes, co-located with the routes that use them. (Prefixing with `_` ensures these folders are not treated as routes by Next.js.)
    -   `api/`: Contains API routes for server-side logic, such as handling form submissions or communicating with the AI model.
    -   Other top-level folders like `about/`, `contact/`, and `privacy/` define static pages.

-   `components/`: Contains globally reusable React components.
    -   `ui/`: Smaller, general-purpose UI components, many of which are from `shadcn/ui`.

-   `config/`: Holds configuration files.
    -   `db.js`: Database connection setup.
    -   `firebaseConfig.js`: Firebase configuration.
    -   `schema.js`: Drizzle ORM database schema definition.

-   `lib/`: Contains utility functions and helper scripts.
    -   `utils.js`: General utility functions.

-   `public/`: Stores all static assets like images, logos, and fonts that are served directly.

## Conventions

### Language

-   The project is primarily written in **JavaScript** using **React** and the **Next.js** framework.
-   **JSX** is used for component syntax.
-   The codebase utilizes modern JavaScript features (ES6+), including `async/await` and arrow functions.
-   For new complex features, **TypeScript** is the preferred language to enhance type safety and maintainability.

### Formatting

-   Code is formatted with an indent size of **2 spaces**.
-   Follows standard **Prettier** conventions for code style.

### Naming

-   **Components**: `PascalCase` (e.g., `Header`, `RoomDesignCard`).
-   **Files**: Component files are named in `PascalCase` (e.g., `Header.jsx`), while other files (like route segments or utility files) typically use `kebab-case` (e.g., `brand-logo.jsx`).
-   **Variables and Functions**: `camelCase` (e.g., `userDetail`, `setUserDetail`).

### Imports

-   **Absolute imports** are configured with a `@/*` alias, pointing to the root directory.
-   Imports are organized with external libraries listed before internal modules and components.

### Error Handling

-   In API routes, server-side logic is wrapped in `try...catch` blocks to handle potential errors gracefully.
-   Errors are returned as a JSON response with a descriptive error message.

### Styling

-   The project uses **Tailwind CSS** for styling, with components from the **shadcn/ui** library.
-   Utility classes are used directly in the JSX for styling components.
-   **Font Consistency:** Ensure consistent font usage as defined in `tailwind.config.js` or global CSS files (e.g., `globals.css`).

### Next.js Specifics

-   Use Server Components by default where applicable. Mark Client Components with `"use client"`.
-   Keep API routes (`app/api/`) clean and focused on their specific task.
-   **Routing:** App Router route groups (e.g., `(auth)`) are used for organization without affecting URL paths, and `layout.js` / `page.js` define route segments.
- **Data Fetching:** Prefer native `fetch` with Next.js caching mechanisms in Server Components.

### Dependencies

This project relies on a set of key libraries to function correctly. Below is a list of the most important dependencies and their roles. For a complete and up-to-date list, always refer to the `package.json` file.

#### Core Frameworks & Libraries

-   **Next.js (`next`):** The core framework for building the React application, handling routing, server-side rendering, and more.
-   **React (`react`):** The fundamental library for building the user interface.
-   **Clerk (`@clerk/nextjs`):** Manages user authentication, including sign-in, sign-up, and session management.
-   **Drizzle ORM (`drizzle-orm`):** A TypeScript-native ORM used for interacting with the PostgreSQL database.
-   **Tailwind CSS (`tailwindcss`):** A utility-first CSS framework for styling the application.

#### UI & Components

-   **shadcn/ui:** A collection of reusable UI components built on top of Radix UI and Tailwind CSS. Key components include:
    -   `@radix-ui/react-alert-dialog`
    -   `@radix-ui/react-dropdown-menu`
    -   `@radix-ui/react-select`
    -   `@radix-ui/react-slot`
-   **Framer Motion (`framer-motion`):** Powers animations and transitions throughout the application.
-   **Lucide React (`lucide-react`):** Provides a comprehensive library of icons.

#### Backend & API

-   **Replicate (`replicate`):** The client library for interacting with the Replicate API to generate AI-powered room designs.
-   **Neon (`@neondatabase/serverless`):** The serverless PostgreSQL database provider.
-   **Resend (`resend`):** Used for sending transactional emails, such as for contact forms or notifications.

#### Utilities

-   **clsx & tailwind-merge:** Utility libraries for conditionally combining and merging Tailwind CSS classes.
-   **axios:** A promise-based HTTP client for making requests to external or internal APIs.

### Environment Configuration

The project's behavior is controlled by environment variables defined in a `.env.local` file. This file is critical for security and must not be committed to version control.

To get started, copy the `.env.example` file to `.env.local` and fill in the required values for the following:

-   **Clerk:** `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY`, `CLERK_SECRET_KEY`
-   **Neon Database:** `NEON_DATABASE_URL`
-   **Replicate AI:** `REPLICATE_API_TOKEN`
-   **Other services** as required.

### Build & Development Scripts

The `package.json` file defines the following scripts for managing the development lifecycle:

-   `npm run dev`: Starts the development server with hot-reloading.
-   `npm run build`: Creates a production-ready build of the application.
-   `npm run start`: Starts the production server.
-   `npm run lint`: Lints the codebase to identify and fix potential errors.


---



---

### 🔧 How to use it

1. **Copy everything inside the grey block** and place it in the tool’s *system* or *developer* message field (depending on the platform).
2. Give your **feature request** in the *user* message, e.g.:






3. The assistant will now:
   * Ask only essential clarifying questions.
   * Read the relevant files (schema, auth helpers, admin routes, UI).
   * Produce a task list and then churn through code, tests, docs, summary—mirroring the “Augment” behaviour but bounded by your rules.

---

### 🧩 Why this works

* **Task list & statuses** let you track progress like a ticketing system.
* **Context scan requirement** prevents “hallucinated” file edits.
* **Explicit diff & build loops** keep changes reviewable.
* **Self‑check & summarize** steps ensure the session ends clean—with docs and next steps spelled out.
* **Rules section** encodes style‑guide items (named constants, no silent deps, etc.) so you don’t have to repeat them each time.

Feel free to tweak any section (rules, icons, wording) to match your team’s conventions.  Happy prompting—and cleaner PRs!



   **This file serves as a guide for the AI. Do not modify this section unless explicitly instructed.**