# Admin Access Control Guide

## 🔒 Security Implementation

Your cleanup tools are now secured with multiple layers of protection to ensure only you can access them.

## 🛡️ Access Control Layers

### 1. **Frontend Access Control**
- **Location**: `app/dashboard/page.jsx`
- **Method**: Email-based whitelist
- **Admin Emails**: Only users with emails in the `adminEmails` array can see the admin tools
- **Current Admin**: `<EMAIL>`

### 2. **API Endpoint Protection**
- **Endpoints**: `/api/debug-credits` and `/api/cleanup-duplicates`
- **Method**: Header-based authentication + email verification
- **Protection**: Checks both admin token and user email

### 3. **Environment Variables**
- **File**: `.env.local`
- **Variables**:
  - `ADMIN_SECRET_TOKEN`: Secret token for API access
  - `ADMIN_EMAILS`: Comma-separated list of admin emails

## 🔧 How to Manage Admin Access

### **Add New Admin Users:**
1. **Frontend**: Update `adminEmails` array in `app/dashboard/page.jsx`
2. **API**: Update `adminEmails` array in both API files
3. **Environment**: Add email to `ADMIN_EMAILS` in `.env.local`

### **Remove Admin Access:**
1. Remove email from all `adminEmails` arrays
2. Update `.env.local` file

### **Change Your Admin Email:**
1. Replace `<EMAIL>` with your actual email in:
   - `app/dashboard/page.jsx` (line ~15)
   - `app/api/debug-credits/route.jsx` (line ~10)
   - `app/api/cleanup-duplicates/route.jsx` (line ~10)
   - `.env.local` (line ~29)

## 🚨 Security Features

### **What Non-Admin Users See:**
- ❌ **No admin tools** visible on dashboard
- ❌ **No cleanup buttons** or controls
- ❌ **API access denied** with 403 error

### **What Admin Users See:**
- ✅ **Admin Tools section** at bottom of dashboard
- ✅ **Red-bordered admin panel** with lock icon
- ✅ **"Check for Duplicates"** button
- ✅ **"Clean Up Duplicates"** button
- ✅ **Detailed results** and user management

### **API Security:**
- ✅ **Email verification** against whitelist
- ✅ **Token-based authentication** for requests
- ✅ **Detailed logging** of access attempts
- ✅ **403 Forbidden** responses for unauthorized users

## 📍 Access Points

### **Dashboard Admin Panel:**
- **URL**: `http://localhost:3001/dashboard`
- **Location**: Scroll to bottom (only visible to admins)
- **Features**: User-friendly interface with visual feedback

### **Direct API Access:**
- **Debug Credits**: `GET http://localhost:3001/api/debug-credits`
- **Cleanup Duplicates**: `GET http://localhost:3001/api/cleanup-duplicates`
- **Headers Required**:
  ```
  x-admin-token: dev-token
  x-user-email: <EMAIL>
  ```

## 🔄 Production Deployment

### **For Production Use:**
1. **Change admin emails** to your production email addresses
2. **Update environment variables** with secure tokens
3. **Remove development fallbacks** in API endpoints
4. **Enable proper Clerk authentication** for enhanced security

### **Environment Variables for Production:**
```env
ADMIN_SECRET_TOKEN=your-super-secure-production-token
ADMIN_EMAILS=<EMAIL>,<EMAIL>
```

## 🧪 Testing Access Control

### **Test as Admin:**
1. Login with admin email (`<EMAIL>`)
2. Go to dashboard - should see admin tools
3. Click buttons - should work normally

### **Test as Non-Admin:**
1. Login with different email
2. Go to dashboard - should NOT see admin tools
3. Try direct API access - should get 403 error

## 📝 Current Configuration

- **Admin Email**: `<EMAIL>`
- **Development Mode**: Enabled (allows dev-token access)
- **API Protection**: Active
- **Frontend Protection**: Active
- **Logging**: Enabled for all access attempts

Your cleanup tools are now **completely secure** and only accessible to you! 🔒
