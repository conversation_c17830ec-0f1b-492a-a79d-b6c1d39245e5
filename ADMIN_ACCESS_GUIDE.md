# Admin Access Control Guide

## 🔒 Simple Database-Driven Admin System

Your admin tools are now controlled by a simple `isAdmin` field in the database.

## 🛡️ How It Works

### 1. **Database Field**
- **Location**: `config/schema.js` - Users table has `isAdmin` boolean field
- **Default**: All new users have `isAdmin: false`
- **Admin Users**: Users with `isAdmin: true` can see admin tools

### 2. **Frontend Control**
- **Location**: `app/dashboard/page.jsx`
- **Logic**: `const isAdmin = userDetail?.isAdmin === true`
- **Display**: Admin tools only show when user has `isAdmin: true`

### 3. **API Protection**
- **Endpoints**: `/api/debug-credits` and `/api/cleanup-duplicates`
- **Method**: Checks user's `isAdmin` field in database
- **Simple**: No tokens needed, just checks database field

### 3. **Environment Variables**
- **File**: `.env.local`
- **Variables**:
  - `ADMIN_SECRET_TOKEN`: Secret token for API access
  - `ADMIN_EMAILS`: Comma-separated list of admin emails

## 🔧 How to Make Yourself Admin

### **Simple Setup:**
1. **Your email is already in `.env.local`**: `ADMIN_EMAILS=<EMAIL>`
2. **Sign up with that email** in your app first
3. **Call the make-admin API**:
   ```bash
   curl -X POST http://localhost:3000/api/make-admin
   ```
   Or visit: `http://localhost:3000/api/make-admin`
4. **Done!** Now you'll see admin tools when you sign in

### **How to Add More Admins:**
- Manually update the database `isAdmin` field to `true` for any user
- Or create a simple admin panel later to promote users

## 🚨 Security Features

### **What Non-Admin Users See:**
- ❌ **No admin tools** visible on dashboard
- ❌ **API access denied** with 403 error

### **What Admin Users See:**
- ✅ **Admin Tools section** at bottom of dashboard
- ✅ **Red-bordered admin panel** with lock icon
- ✅ **"Check for Duplicates"** button
- ✅ **"Clean Up Duplicates"** button

### **Simple Security:**
- ✅ **Database field check** - only `isAdmin: true` users get access
- ✅ **No tokens needed** - just checks the database
- ✅ **Clean and simple** - easy to understand and maintain

## 📍 Access Points

### **Dashboard Admin Panel:**
- **URL**: `http://localhost:3001/dashboard`
- **Location**: Scroll to bottom (only visible to admins)
- **Features**: User-friendly interface with visual feedback

### **Direct API Access:**
- **Debug Credits**: `GET http://localhost:3001/api/debug-credits`
- **Cleanup Duplicates**: `GET http://localhost:3001/api/cleanup-duplicates`
- **Headers Required**:
  ```
  x-admin-token: dev-token
  x-user-email: <EMAIL>
  ```

## 🔄 Production Deployment

### **For Production Use:**
1. **Change admin emails** to your production email addresses
2. **Update environment variables** with secure tokens
3. **Remove development fallbacks** in API endpoints
4. **Enable proper Clerk authentication** for enhanced security

### **Environment Variables for Production:**
```env
ADMIN_SECRET_TOKEN=your-super-secure-production-token
ADMIN_EMAILS=<EMAIL>,<EMAIL>
```

## 🧪 Testing Access Control

### **Test as Admin:**
1. Login with admin email (`<EMAIL>`)
2. Go to dashboard - should see admin tools
3. Click buttons - should work normally

### **Test as Non-Admin:**
1. Login with different email
2. Go to dashboard - should NOT see admin tools
3. Try direct API access - should get 403 error

## 📝 Current Configuration

- **Admin Email**: `<EMAIL>`
- **Development Mode**: Enabled (allows dev-token access)
- **API Protection**: Active
- **Frontend Protection**: Active
- **Logging**: Enabled for all access attempts

Your cleanup tools are now **completely secure** and only accessible to you! 🔒
