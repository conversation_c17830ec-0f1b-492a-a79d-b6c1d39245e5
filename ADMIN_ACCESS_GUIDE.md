# Admin Access Control Guide

## 🔒 Security Implementation - UPDATED TO DATABASE-DRIVEN SYSTEM

Your cleanup tools are now secured with a database-driven admin system for better security and management.

## 🛡️ Access Control Layers

### 1. **Database-Driven Access Control**
- **Location**: `config/schema.js` - Users table with `isAdmin` field
- **Method**: Database field check (`isAdmin: boolean`)
- **Default**: All new users have `isAdmin: false`
- **Management**: Use admin management API or script to promote users

### 2. **Frontend Access Control**
- **Location**: `app/dashboard/page.jsx`
- **Method**: Checks `userDetail.isAdmin` from database
- **Display**: Admin tools only visible when `isAdmin === true`

### 3. **API Endpoint Protection**
- **Endpoints**: `/api/debug-credits` and `/api/cleanup-duplicates`
- **Method**: Database lookup to verify admin status
- **Protection**: Queries database to check user's `isAdmin` field

### 3. **Environment Variables**
- **File**: `.env.local`
- **Variables**:
  - `ADMIN_SECRET_TOKEN`: Secret token for API access
  - `ADMIN_EMAILS`: Comma-separated list of admin emails

## 🔧 How to Manage Admin Access

### **Promote User to Admin:**
1. **Using Script**: `node scripts/promote-admin.js <EMAIL>`
2. **Using API**: POST to `/api/admin-management` with super admin key
3. **Required**: Set `SUPER_ADMIN_KEY` in `.env.local`

### **Demote Admin User:**
1. **Using API**: POST to `/api/admin-management` with `isAdmin: false`
2. **Required**: Super admin key for authentication

### **List All Admins:**
1. **Using API**: GET `/api/admin-management` with super admin key
2. **Returns**: All users with `isAdmin: true`

### **First Time Setup:**
1. Add `SUPER_ADMIN_KEY=your-secret-key` to `.env.local`
2. Run `node scripts/promote-admin.js <EMAIL>`
3. Verify admin access in dashboard

## 🚨 Security Features

### **What Non-Admin Users See:**
- ❌ **No admin tools** visible on dashboard
- ❌ **No cleanup buttons** or controls
- ❌ **API access denied** with 403 error

### **What Admin Users See:**
- ✅ **Admin Tools section** at bottom of dashboard
- ✅ **Red-bordered admin panel** with lock icon
- ✅ **"Check for Duplicates"** button
- ✅ **"Clean Up Duplicates"** button
- ✅ **Detailed results** and user management

### **API Security:**
- ✅ **Email verification** against whitelist
- ✅ **Token-based authentication** for requests
- ✅ **Detailed logging** of access attempts
- ✅ **403 Forbidden** responses for unauthorized users

## 📍 Access Points

### **Dashboard Admin Panel:**
- **URL**: `http://localhost:3001/dashboard`
- **Location**: Scroll to bottom (only visible to admins)
- **Features**: User-friendly interface with visual feedback

### **Direct API Access:**
- **Debug Credits**: `GET http://localhost:3001/api/debug-credits`
- **Cleanup Duplicates**: `GET http://localhost:3001/api/cleanup-duplicates`
- **Headers Required**:
  ```
  x-admin-token: dev-token
  x-user-email: <EMAIL>
  ```

## 🔄 Production Deployment

### **For Production Use:**
1. **Change admin emails** to your production email addresses
2. **Update environment variables** with secure tokens
3. **Remove development fallbacks** in API endpoints
4. **Enable proper Clerk authentication** for enhanced security

### **Environment Variables for Production:**
```env
ADMIN_SECRET_TOKEN=your-super-secure-production-token
ADMIN_EMAILS=<EMAIL>,<EMAIL>
```

## 🧪 Testing Access Control

### **Test as Admin:**
1. Login with admin email (`<EMAIL>`)
2. Go to dashboard - should see admin tools
3. Click buttons - should work normally

### **Test as Non-Admin:**
1. Login with different email
2. Go to dashboard - should NOT see admin tools
3. Try direct API access - should get 403 error

## 📝 Current Configuration

- **Admin Email**: `<EMAIL>`
- **Development Mode**: Enabled (allows dev-token access)
- **API Protection**: Active
- **Frontend Protection**: Active
- **Logging**: Enabled for all access attempts

Your cleanup tools are now **completely secure** and only accessible to you! 🔒
