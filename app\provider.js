"use client"
import { useUser } from '@clerk/nextjs'
import axios from 'axios';
import React, { useEffect, useState, useCallback } from 'react'
import { UserDetailContext } from './_context/UserDetailContext';
import { PayPalScriptProvider } from '@paypal/react-paypal-js';
import { ThemeProvider } from './_context/ThemeContext';

function Provider({ children }) {

    const { user, isLoaded } = useUser();
    const [userDetail, setUserDetail] = useState(null);
    const [isUserVerified, setIsUserVerified] = useState(false);

    const VerifyUser = useCallback(async () => {
        if (isLoaded && user && !isUserVerified) {
            try {
                const dataResult = await axios.post('/api/verify-user', {
                    user: user
                });
                setUserDetail(dataResult.data.result);
                setIsUserVerified(true); // Mark as verified to prevent re-fetching
            } catch (error) {
                console.error("Error verifying user:", error);
                // Optionally handle the error, e.g., show a notification
            }
        }
    }, [isLoaded, user, isUserVerified]);

    useEffect(() => {
        VerifyUser();
    }, [VerifyUser]);

    return (
        <ThemeProvider>
            <UserDetailContext.Provider value={{ userDetail, setUserDetail }}>
                <PayPalScriptProvider options={{ clientId: process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID }}>
                    {isLoaded && userDetail !== null ? (
                        <div>
                            {children}
                        </div>
                    ) : (
                        // Optionally, render a loading spinner or null while waiting
                        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>Loading...</div>
                    )}
                </PayPalScriptProvider>
            </UserDetailContext.Provider>
        </ThemeProvider>
    )
}

export default Provider