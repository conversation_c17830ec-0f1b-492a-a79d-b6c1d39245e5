#!/usr/bin/env node

/**
 * Admin Promotion Script
 * 
 * This script helps you promote a user to admin status.
 * Usage: node scripts/promote-admin.js <email>
 * 
 * Make sure to set SUPER_ADMIN_KEY in your .env.local file first!
 */

const readline = require('readline');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function askQuestion(question) {
    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            resolve(answer);
        });
    });
}

async function promoteAdmin() {
    try {
        console.log('🔐 Admin Promotion Tool');
        console.log('========================\n');

        // Get email from command line or ask for it
        let email = process.argv[2];
        
        if (!email) {
            email = await askQuestion('Enter the email address to promote to admin: ');
        }

        if (!email || !email.includes('@')) {
            console.error('❌ Please provide a valid email address');
            process.exit(1);
        }

        // Get super admin key
        const superAdminKey = await askQuestion('Enter the SUPER_ADMIN_KEY (from your .env.local): ');
        
        if (!superAdminKey) {
            console.error('❌ Super admin key is required');
            process.exit(1);
        }

        console.log(`\n🔄 Promoting ${email} to admin...`);

        // Make API call to promote user
        const response = await fetch('http://localhost:3000/api/admin-management', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'x-super-admin-key': superAdminKey
            },
            body: JSON.stringify({
                email: email,
                isAdmin: true
            })
        });

        const result = await response.json();

        if (result.success) {
            console.log('✅ Success!');
            console.log(`📧 User: ${result.user.name} (${result.user.email})`);
            console.log(`👑 Admin Status: ${result.user.isAdmin ? 'ADMIN' : 'USER'}`);
            console.log('\n🎉 User has been promoted to admin!');
        } else {
            console.error('❌ Failed to promote user:');
            console.error(`   ${result.error}`);
            
            if (response.status === 403) {
                console.log('\n💡 Tips:');
                console.log('   - Make sure SUPER_ADMIN_KEY is set in your .env.local file');
                console.log('   - Ensure your Next.js development server is running');
            } else if (response.status === 404) {
                console.log('\n💡 Tips:');
                console.log('   - Make sure the user has signed up and exists in the database');
                console.log('   - Check that the email address is correct');
            }
        }

    } catch (error) {
        console.error('❌ Error:', error.message);
        
        if (error.code === 'ECONNREFUSED') {
            console.log('\n💡 Make sure your Next.js development server is running:');
            console.log('   npm run dev');
        }
    } finally {
        rl.close();
    }
}

// Run the script
promoteAdmin();
