"use client"
import React, { useState, useContext } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { UserDetailContext } from '@/app/_context/UserDetailContext'

export default function CleanupTool() {
    const [loading, setLoading] = useState(false)
    const [result, setResult] = useState(null)
    const { userDetail } = useContext(UserDetailContext)

    const checkForDuplicates = async () => {
        setLoading(true)
        setResult(null)

        try {
            const response = await fetch('/api/debug-credits', {
                headers: {
                    'x-user-email': userDetail?.email || ''
                }
            })
            const data = await response.json()
            
            if (data.success) {
                const users = data.users
                const emailGroups = {}
                let duplicateCount = 0
                
                // Group by email to detect duplicates
                users.forEach(user => {
                    if (!emailGroups[user.email]) {
                        emailGroups[user.email] = []
                    }
                    emailGroups[user.email].push(user)
                })
                
                // Count duplicates
                Object.values(emailGroups).forEach(group => {
                    if (group.length > 1) {
                        duplicateCount += group.length - 1
                    }
                })
                
                setResult({
                    type: 'check',
                    totalUsers: users.length,
                    duplicates: duplicateCount,
                    users: users,
                    emailGroups: emailGroups
                })
            } else {
                setResult({
                    type: 'error',
                    message: data.error || 'Failed to check users'
                })
            }
        } catch (error) {
            setResult({
                type: 'error',
                message: error.message
            })
        } finally {
            setLoading(false)
        }
    }

    const cleanupDuplicates = async () => {
        setLoading(true)
        setResult(null)

        try {
            const response = await fetch('/api/cleanup-duplicates', {
                headers: {
                    'x-user-email': userDetail?.email || ''
                }
            })
            const data = await response.json()
            
            setResult({
                type: 'cleanup',
                success: data.success,
                message: data.message,
                deletedUsers: data.deletedUsers || [],
                remainingUsers: data.remainingUsers || 0
            })
        } catch (error) {
            setResult({
                type: 'error',
                message: error.message
            })
        } finally {
            setLoading(false)
        }
    }

    return (
        <Card className="w-full max-w-2xl">
            <CardHeader>
                <CardTitle>User Management Tools</CardTitle>
                <CardDescription>
                    Check for and clean up duplicate users in the system
                </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
                <div className="flex gap-2">
                    <Button 
                        onClick={checkForDuplicates}
                        disabled={loading}
                        variant="outline"
                    >
                        {loading ? 'Checking...' : 'Check for Duplicates'}
                    </Button>
                    
                    <Button 
                        onClick={cleanupDuplicates}
                        disabled={loading}
                        variant="destructive"
                    >
                        {loading ? 'Cleaning...' : 'Clean Up Duplicates'}
                    </Button>
                </div>

                {result && (
                    <div className="mt-4 p-4 border rounded-lg">
                        {result.type === 'check' && (
                            <div>
                                <h3 className="font-semibold mb-2">User Check Results:</h3>
                                <p><strong>Total Users:</strong> {result.totalUsers}</p>
                                <p><strong>Duplicates Found:</strong> {result.duplicates}</p>
                                
                                {result.duplicates > 0 && (
                                    <div className="mt-2">
                                        <p className="text-orange-600 font-medium">
                                            ⚠️ Duplicates detected! Click "Clean Up Duplicates" to remove them.
                                        </p>
                                    </div>
                                )}
                                
                                {result.duplicates === 0 && (
                                    <p className="text-green-600 font-medium">
                                        ✅ No duplicates found! All users are unique.
                                    </p>
                                )}

                                <div className="mt-3">
                                    <h4 className="font-medium mb-1">User List:</h4>
                                    <div className="text-sm space-y-1 text-gray-800">
                                        {result.users.map((user, index) => {
                                            const duplicateCount = result.emailGroups[user.email].length
                                            const isDuplicate = duplicateCount > 1
                                            return (
                                                <div 
                                                    key={user.id} 
                                                    className={`p-2 rounded ${isDuplicate ? 'bg-red-50 border border-red-200' : 'bg-gray-50'}`}
                                                >
                                                    <span className="font-mono text-xs">ID:{user.id}</span> - 
                                                    <span className="ml-1">{user.email}</span> - 
                                                    <span className="ml-1">{user.credits} credits</span>
                                                    {isDuplicate && (
                                                        <span className="ml-2 text-red-600 font-medium">
                                                            (DUPLICATE - {duplicateCount} entries)
                                                        </span>
                                                    )}
                                                </div>
                                            )
                                        })}
                                    </div>
                                </div>
                            </div>
                        )}

                        {result.type === 'cleanup' && (
                            <div>
                                <h3 className="font-semibold mb-2">Cleanup Results:</h3>
                                {result.success ? (
                                    <div>
                                        <p className="text-green-600 font-medium">✅ {result.message}</p>
                                        {result.deletedUsers.length > 0 && (
                                            <div className="mt-2">
                                                <p><strong>Deleted Users:</strong></p>
                                                <ul className="list-disc list-inside text-sm">
                                                    {result.deletedUsers.map(user => (
                                                        <li key={user.id}>
                                                            ID:{user.id} - {user.email}
                                                        </li>
                                                    ))}
                                                </ul>
                                            </div>
                                        )}
                                        <p className="mt-2"><strong>Remaining Users:</strong> {result.remainingUsers}</p>
                                    </div>
                                ) : (
                                    <p className="text-red-600 font-medium">❌ {result.message}</p>
                                )}
                            </div>
                        )}

                        {result.type === 'error' && (
                            <div>
                                <h3 className="font-semibold mb-2 text-red-600">Error:</h3>
                                <p className="text-red-600">{result.message}</p>
                            </div>
                        )}
                    </div>
                )}
            </CardContent>
        </Card>
    )
}
