import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';
import { db } from '@/config/db';
import { Users } from '@/config/schema';
import { eq } from 'drizzle-orm';

const isProtectedRoute = createRouteMatcher(['/dashboard(.*)']);

export default clerkMiddleware(async (auth, req) => {
  if (isProtectedRoute(req)) {
    const { userId, sessionClaims } = auth();

    // If user is not authenticated by <PERSON>, redirect to sign-in
    if (!userId) {
      return auth().redirectToSignIn(); // Use Clerk's built-in redirect
    }

    // If authenticated, proceed to check database
    try {
      const userEmail = sessionClaims?.primaryEmailAddress?.emailAddress;
      if (!userEmail) {
        // This case should ideally not happen if userId is present
        return NextResponse.redirect(new URL('/sign-in', req.url));
      }

      const userInDb = await db.select().from(Users).where(eq(Users.email, userEmail));

      if (userInDb.length === 0) {
        // User authenticated with Clerk but not in our DB
        const signInUrl = new URL('/sign-in', req.url);
        signInUrl.searchParams.set('error', 'db_user_not_found');
        return NextResponse.redirect(signInUrl);
      }
      // If user is authenticated and found in DB, allow access
      return NextResponse.next();

    } catch (error) {
      console.error("Middleware database error:", error);
      const signInUrl = new URL('/sign-in', req.url);
      signInUrl.searchParams.set('error', 'middleware_db_error');
      return NextResponse.redirect(signInUrl);
    }
  }

  return NextResponse.next();
});
export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
}