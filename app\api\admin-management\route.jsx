import { db } from "@/config/db";
import { Users } from "@/config/schema";
import { NextResponse } from "next/server";
import { eq } from "drizzle-orm";

// Super admin secret key for initial admin setup
const SUPER_ADMIN_KEY = process.env.SUPER_ADMIN_KEY || 'change-this-secret-key';

async function checkSuperAdminAccess(req) {
    try {
        const superAdminKey = req.headers.get('x-super-admin-key');
        
        if (!superAdminKey || superAdminKey !== SUPER_ADMIN_KEY) {
            console.log('Invalid or missing super admin key');
            return { isAuthorized: false, error: 'Invalid super admin key' };
        }

        return { isAuthorized: true };
    } catch (error) {
        console.error('Error checking super admin access:', error);
        return { isAuthorized: false, error: 'Access check failed' };
    }
}

// POST: Promote user to admin or demote from admin
export async function POST(req) {
    // Check super admin access
    const { isAuthorized, error } = await checkSuperAdminAccess(req);

    if (!isAuthorized) {
        return NextResponse.json({
            success: false,
            error: 'Access denied. Super admin key required.'
        }, { status: 403 });
    }

    try {
        const { email, isAdmin } = await req.json();

        if (!email) {
            return NextResponse.json({
                success: false,
                error: 'Email is required'
            }, { status: 400 });
        }

        if (typeof isAdmin !== 'boolean') {
            return NextResponse.json({
                success: false,
                error: 'isAdmin must be a boolean value'
            }, { status: 400 });
        }

        // Check if user exists
        const userInfo = await db.select().from(Users)
            .where(eq(Users.email, email));

        if (userInfo.length === 0) {
            return NextResponse.json({
                success: false,
                error: 'User not found'
            }, { status: 404 });
        }

        // Update user admin status
        const updatedUser = await db.update(Users)
            .set({ isAdmin: isAdmin })
            .where(eq(Users.email, email))
            .returning({
                id: Users.id,
                name: Users.name,
                email: Users.email,
                isAdmin: Users.isAdmin
            });

        console.log(`User ${email} admin status updated to: ${isAdmin}`);

        return NextResponse.json({
            success: true,
            message: `User ${isAdmin ? 'promoted to' : 'demoted from'} admin`,
            user: updatedUser[0]
        });

    } catch (error) {
        console.error('Error updating admin status:', error);
        return NextResponse.json({
            success: false,
            error: 'Failed to update admin status'
        }, { status: 500 });
    }
}

// GET: List all admin users
export async function GET(req) {
    // Check super admin access
    const { isAuthorized, error } = await checkSuperAdminAccess(req);

    if (!isAuthorized) {
        return NextResponse.json({
            success: false,
            error: 'Access denied. Super admin key required.'
        }, { status: 403 });
    }

    try {
        // Get all admin users
        const adminUsers = await db.select({
            id: Users.id,
            name: Users.name,
            email: Users.email,
            isAdmin: Users.isAdmin,
            credits: Users.credits
        }).from(Users)
        .where(eq(Users.isAdmin, true));

        return NextResponse.json({
            success: true,
            adminUsers: adminUsers,
            count: adminUsers.length
        });

    } catch (error) {
        console.error('Error fetching admin users:', error);
        return NextResponse.json({
            success: false,
            error: 'Failed to fetch admin users'
        }, { status: 500 });
    }
}
