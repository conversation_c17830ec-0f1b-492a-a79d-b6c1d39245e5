import { NextResponse } from 'next/server';
import OpenAI from 'openai';
import { styleReferenceMap } from '@/lib/styleReferenceMap';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function POST(req) {
  try {
    const body = await req.json();
    const { roomType, designType, mood, roomFeatures, additionalReq } = body;

    if (!roomType || !designType) {
      return NextResponse.json(
        { error: 'roomType and designType are required' },
        { status: 400 }
      );
    }

    const moodString =
      Array.isArray(mood) && mood.length > 0 ? mood.join(', ') : null;

    const featuresString =
      Array.isArray(roomFeatures) && roomFeatures.length > 0
        ? roomFeatures.join(', ')
        : null;

    let userInput = `Room Type: ${roomType}\nDesign Style: ${designType}`;
    if (moodString) userInput += `\nMoods: ${moodString}`;
    if (featuresString) userInput += `\nRoom Features: ${featuresString}`;
    if (additionalReq) userInput += `\nAdditional Requirements: ${additionalReq}`;

    const styleData = styleReferenceMap[designType.toLowerCase()];
    let enrichedDescription = '';

    if (styleData) {
      enrichedDescription = `
Style-Specific Guidance:
Use elements such as ${styleData.keywords.join(', ')}.
Incorporate a color palette of ${styleData.colorPalette}, using materials like ${styleData.materials},
and add accents such as ${styleData.accents}.`;
    }

    const prompt = `You are an expert interior designer. Create a richly detailed, image-generation-friendly description for an interior room transformation based on the provided inputs.

User Input:
${userInput}
${enrichedDescription}

Instructions:
- Write one short, vivid paragraph describing the visual appearance of the room.
- Emphasize the style through visual cues: materials, layout, lighting, furniture, accessories, and palette.
- Integrate moods and features naturally if provided.
- Be specific to the design style — use accurate, recognizable elements that represent it.
- Do not mention style name or room type explicitly.
- Keep it under 80 words. Be concise and visual.

Generate the description now.`;

    const response = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [
        { role: 'system', content: 'You are a helpful assistant.' },
        { role: 'user', content: prompt },
      ],
      max_tokens: 150,
      temperature: 0.7,
    });

    const wellFormedPrompt = response.choices[0]?.message?.content?.trim();

    return NextResponse.json({ wellFormedPrompt });
  } catch (error) {
    console.error('Error generating well-formed prompt:', error);

    return NextResponse.json(
      {
        error: 'Failed to generate well-formed prompt',
        details: error?.message || 'Unknown error',
      },
      { status: 500 }
    );
  }
}
