import Redis from 'ioredis';

// Initialize Redis client
// Use process.env.REDIS_URL for production, fallback to local for development
const redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');

/**
 * Redis-based rate limiter.
 * Uses a sliding window approach.
 * @param {string} key - The key to rate limit by (e.g., IP address, user ID).
 * @param {number} limit - The maximum number of requests allowed within the window.
 * @param {number} windowMs - The time window in milliseconds.
 * @returns {Promise<{success: boolean, retryAfter?: number}>}
 */
export async function checkRedisRateLimit(key, limit, windowMs) {
    const now = Date.now();
    const windowStart = now - windowMs;

    // Use a Redis pipeline for atomic operations
    const pipeline = redis.pipeline();

    // Remove old timestamps outside the current window
    pipeline.zremrangebyscore(key, 0, windowStart);
    // Add current request timestamp
    pipeline.zadd(key, now, now);
    // Set expiration for the key to clean up memory
    pipeline.expire(key, Math.ceil(windowMs / 1000)); // Convert ms to seconds
    // Get the count of requests within the window
    pipeline.zcard(key);

    const results = await pipeline.exec();

    // The count is the last result in the pipeline
    const requestCount = results[results.length - 1][1];

    if (requestCount <= limit) {
        return { success: true };
    } else {
        // Calculate when the user can retry
        const oldestTimestamp = await redis.zrange(key, 0, 0);
        const retryAfter = (parseInt(oldestTimestamp[0], 10) + windowMs - now) / 1000;
        return { success: false, retryAfter: Math.ceil(retryAfter) };
    }
}
